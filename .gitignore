# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database files
*.db
*.sqlite
*.sqlite3
backups/

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
tmp/
temp/
*.tmp

# Config files with sensitive data
config.local.js
secrets.js

# Build outputs
dist/
build/

# Coverage reports
coverage/
.nyc_output/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Railway specific
.railway/

# Docker
.dockerignore
docker-compose.override.yml

# Certificates and keys
*.crt
*.csr
*.key
*.pem
*.p12
*.pfx

# Configuration files with sensitive data
config.production.js
secrets.json

# Archive files
*.zip
*.tar.gz
*.rar

# Performance monitoring
newrelic_agent.log

# Process managers
.pm2/

# Serverless directories
.serverless/

# Error logs
error.log
debug.log

# Local development
local/
dev/

# Backup files
*.backup
*.bak
