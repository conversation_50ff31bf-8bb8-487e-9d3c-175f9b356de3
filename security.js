const crypto = require('crypto');
const { config } = require('./config');

class SecurityManager {
    constructor() {
        this.rateLimitMap = new Map(); // تتبع معدل الطلبات
        this.failedAttempts = new Map(); // تتبع المحاولات الفاشلة
        this.blockedIPs = new Set(); // قائمة IP المحظورة
        this.sessionTokens = new Map(); // رموز الجلسات
    }

    // فحص معدل الطلبات
    checkRateLimit(userId) {
        const now = Date.now();
        const windowStart = now - config.telegram.security.rateLimitWindow;
        
        if (!this.rateLimitMap.has(userId)) {
            this.rateLimitMap.set(userId, []);
        }
        
        const userRequests = this.rateLimitMap.get(userId);
        
        // إزالة الطلبات القديمة
        const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
        this.rateLimitMap.set(userId, validRequests);
        
        // فحص الحد الأقصى
        if (validRequests.length >= config.telegram.security.maxRequestsPerWindow) {
            return false;
        }
        
        // إضافة الطلب الحالي
        validRequests.push(now);
        return true;
    }

    // تسجيل محاولة فاشلة
    recordFailedAttempt(userId) {
        const now = Date.now();
        
        if (!this.failedAttempts.has(userId)) {
            this.failedAttempts.set(userId, []);
        }
        
        const attempts = this.failedAttempts.get(userId);
        attempts.push(now);
        
        // الاحتفاظ بآخر 10 محاولات فقط
        if (attempts.length > 10) {
            attempts.shift();
        }
        
        // فحص إذا تم تجاوز الحد المسموح
        const recentAttempts = attempts.filter(
            timestamp => now - timestamp < config.security.protection.lockoutDuration
        );
        
        if (recentAttempts.length >= config.security.protection.maxFailedAttempts) {
            this.blockUser(userId);
            return true; // مستخدم محظور
        }
        
        return false;
    }

    // حظر مستخدم
    blockUser(userId) {
        this.blockedIPs.add(userId);
        console.log(`🚫 تم حظر المستخدم: ${userId}`);
        
        // إزالة الحظر تلقائياً بعد فترة
        setTimeout(() => {
            this.unblockUser(userId);
        }, config.security.protection.lockoutDuration);
    }

    // إلغاء حظر مستخدم
    unblockUser(userId) {
        this.blockedIPs.delete(userId);
        this.failedAttempts.delete(userId);
        console.log(`✅ تم إلغاء حظر المستخدم: ${userId}`);
    }

    // فحص إذا كان المستخدم محظور
    isUserBlocked(userId) {
        return this.blockedIPs.has(userId);
    }

    // تشفير البيانات الحساسة
    encrypt(text) {
        if (!config.security.encryption.enabled) {
            return text;
        }
        
        try {
            const algorithm = config.security.encryption.algorithm;
            const key = this.getEncryptionKey();
            const iv = crypto.randomBytes(16);
            
            const cipher = crypto.createCipher(algorithm, key);
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            return iv.toString('hex') + ':' + encrypted;
        } catch (error) {
            console.error('❌ خطأ في التشفير:', error.message);
            return text;
        }
    }

    // فك تشفير البيانات
    decrypt(encryptedText) {
        if (!config.security.encryption.enabled) {
            return encryptedText;
        }
        
        try {
            const algorithm = config.security.encryption.algorithm;
            const key = this.getEncryptionKey();
            
            const parts = encryptedText.split(':');
            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = parts[1];
            
            const decipher = crypto.createDecipher(algorithm, key);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('❌ خطأ في فك التشفير:', error.message);
            return encryptedText;
        }
    }

    // الحصول على مفتاح التشفير
    getEncryptionKey() {
        const key = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
        return crypto.createHash('sha256').update(key).digest();
    }

    // إنشاء رمز جلسة آمن
    generateSessionToken(userId) {
        const token = crypto.randomBytes(32).toString('hex');
        const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 ساعة
        
        this.sessionTokens.set(token, {
            userId,
            expiresAt,
            createdAt: Date.now()
        });
        
        return token;
    }

    // التحقق من صحة رمز الجلسة
    validateSessionToken(token) {
        const session = this.sessionTokens.get(token);
        
        if (!session) {
            return null;
        }
        
        if (Date.now() > session.expiresAt) {
            this.sessionTokens.delete(token);
            return null;
        }
        
        return session;
    }

    // إلغاء رمز الجلسة
    revokeSessionToken(token) {
        this.sessionTokens.delete(token);
    }

    // تنظيف الجلسات المنتهية الصلاحية
    cleanupExpiredSessions() {
        const now = Date.now();
        
        for (const [token, session] of this.sessionTokens.entries()) {
            if (now > session.expiresAt) {
                this.sessionTokens.delete(token);
            }
        }
    }

    // تنظيف البيانات القديمة
    cleanup() {
        const now = Date.now();
        const cleanupThreshold = 24 * 60 * 60 * 1000; // 24 ساعة
        
        // تنظيف معدل الطلبات
        for (const [userId, requests] of this.rateLimitMap.entries()) {
            const validRequests = requests.filter(timestamp => now - timestamp < cleanupThreshold);
            if (validRequests.length === 0) {
                this.rateLimitMap.delete(userId);
            } else {
                this.rateLimitMap.set(userId, validRequests);
            }
        }
        
        // تنظيف المحاولات الفاشلة
        for (const [userId, attempts] of this.failedAttempts.entries()) {
            const validAttempts = attempts.filter(timestamp => now - timestamp < cleanupThreshold);
            if (validAttempts.length === 0) {
                this.failedAttempts.delete(userId);
            } else {
                this.failedAttempts.set(userId, validAttempts);
            }
        }
        
        // تنظيف الجلسات المنتهية
        this.cleanupExpiredSessions();
        
        console.log('🧹 تم تنظيف بيانات الأمان');
    }

    // بدء التنظيف التلقائي
    startAutoCleanup() {
        // تنظيف كل ساعة
        setInterval(() => {
            this.cleanup();
        }, 60 * 60 * 1000);
        
        console.log('🔄 تم بدء التنظيف التلقائي للأمان');
    }

    // إيقاف التنظيف التلقائي
    stopAutoCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            console.log('⏹️ تم إيقاف التنظيف التلقائي للأمان');
        }
    }

    // الحصول على إحصائيات الأمان
    getSecurityStats() {
        return {
            rateLimitEntries: this.rateLimitMap.size,
            failedAttempts: this.failedAttempts.size,
            blockedUsers: this.blockedIPs.size,
            activeSessions: this.sessionTokens.size,
            encryptionEnabled: config.security.encryption.enabled
        };
    }
}

// إنشاء مثيل واحد للاستخدام العام
const securityManager = new SecurityManager();

// بدء التنظيف التلقائي
securityManager.startAutoCleanup();

module.exports = securityManager;
