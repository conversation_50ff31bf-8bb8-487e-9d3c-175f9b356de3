const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { config } = require('./config');

class Database {
    constructor() {
        this.databaseUrl = config.database.url;
        this.dbType = this.detectDatabaseType(this.databaseUrl);

        console.log(`🗄️ نوع قاعدة البيانات: ${this.dbType}`);

        this.setupDatabase();
        this.initialized = false;
    }

    detectDatabaseType(url) {
        if (url.startsWith('postgresql://') || url.startsWith('postgres://')) return 'postgresql';
        if (url.startsWith('mysql://') || url.startsWith('mysql2://')) return 'mysql';
        if (url.startsWith('mariadb://')) return 'mariadb';
        if (url.startsWith('mssql://') || url.startsWith('sqlserver://')) return 'mssql';
        if (url.startsWith('oracle://')) return 'oracle';
        if (url.startsWith('mongodb://') || url.startsWith('mongodb+srv://')) return 'mongodb';
        if (url.startsWith('redis://')) return 'redis';
        return 'sqlite';
    }

    setupDatabase() {
        switch (this.dbType) {
            case 'postgresql':
                this.setupPostgreSQL();
                break;
            case 'mysql':
                this.setupMySQL();
                break;
            case 'mariadb':
                this.setupMariaDB();
                break;
            case 'mssql':
                this.setupMSSQL();
                break;
            case 'oracle':
                this.setupOracle();
                break;
            case 'mongodb':
                this.setupMongoDB();
                break;
            case 'redis':
                this.setupRedis();
                break;
            default:
                this.setupSQLite();
        }
    }

    setupPostgreSQL() {
        try {
            const { Pool } = require('pg');
            this.pool = new Pool({
                connectionString: this.databaseUrl,
                ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
            });
            this.db = null;
            console.log('✅ تم إعداد PostgreSQL');
        } catch (error) {
            console.error('❌ PostgreSQL غير متوفر، سيتم استخدام SQLite');
            this.isPostgreSQL = false;
            this.isSQLite = true;
            this.setupSQLite();
        }
    }

    setupMySQL() {
        try {
            const mysql = require('mysql2/promise');
            this.pool = mysql.createPool({
                uri: this.databaseUrl,
                waitForConnections: true,
                connectionLimit: 10,
                queueLimit: 0
            });
            this.db = null;
            console.log('✅ تم إعداد MySQL');
        } catch (error) {
            console.error('❌ MySQL غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupMariaDB() {
        try {
            const mariadb = require('mariadb');
            this.pool = mariadb.createPool({
                connectionString: this.databaseUrl,
                connectionLimit: 10
            });
            this.db = null;
            console.log('✅ تم إعداد MariaDB');
        } catch (error) {
            console.error('❌ MariaDB غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupMSSQL() {
        try {
            const { ConnectionPool } = require('tedious');
            const { parse } = require('url');
            const parsedUrl = parse(this.databaseUrl);

            this.pool = new ConnectionPool({
                server: parsedUrl.hostname,
                authentication: {
                    type: 'default',
                    options: {
                        userName: parsedUrl.auth ? parsedUrl.auth.split(':')[0] : '',
                        password: parsedUrl.auth ? parsedUrl.auth.split(':')[1] : ''
                    }
                },
                options: {
                    database: parsedUrl.pathname ? parsedUrl.pathname.substring(1) : '',
                    encrypt: true,
                    trustServerCertificate: true
                }
            });
            this.db = null;
            console.log('✅ تم إعداد SQL Server');
        } catch (error) {
            console.error('❌ SQL Server غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupOracle() {
        try {
            const oracledb = require('oracledb');
            this.pool = oracledb.createPool({
                connectString: this.databaseUrl,
                poolMin: 2,
                poolMax: 10,
                poolIncrement: 1
            });
            this.db = null;
            console.log('✅ تم إعداد Oracle Database');
        } catch (error) {
            console.error('❌ Oracle Database غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupMongoDB() {
        try {
            const { MongoClient } = require('mongodb');
            this.client = new MongoClient(this.databaseUrl);
            this.db = null;
            this.pool = null;
            console.log('✅ تم إعداد MongoDB');
        } catch (error) {
            console.error('❌ MongoDB غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupRedis() {
        try {
            const { createClient } = require('redis');
            this.client = createClient({ url: this.databaseUrl });
            this.db = null;
            this.pool = null;
            console.log('✅ تم إعداد Redis');
        } catch (error) {
            console.error('❌ Redis غير متوفر، سيتم استخدام SQLite');
            this.dbType = 'sqlite';
            this.setupSQLite();
        }
    }

    setupSQLite() {
        const dbPath = this.databaseUrl.replace('sqlite:', '') || path.join(__dirname, 'minecraft_bot.db');
        this.db = new sqlite3.Database(dbPath);
        this.pool = null;
    }

    async init() {
        if (!this.initialized) {
            switch (this.dbType) {
                case 'postgresql':
                    await this.initPostgreSQLTables();
                    break;
                case 'mysql':
                case 'mariadb':
                    await this.initMySQLTables();
                    break;
                case 'mssql':
                    await this.initMSSQLTables();
                    break;
                case 'oracle':
                    await this.initOracleTables();
                    break;
                case 'mongodb':
                    await this.initMongoCollections();
                    break;
                case 'redis':
                    await this.initRedisStructure();
                    break;
                default:
                    await this.initSQLiteTables();
            }
            this.initialized = true;
        }
        return this;
    }

    async initPostgreSQLTables() {
        const client = await this.pool.connect();
        try {
            // جدول المستخدمين
            await client.query(`
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    telegram_id BIGINT UNIQUE NOT NULL,
                    username TEXT,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // جدول البوتات
            await client.query(`
                CREATE TABLE IF NOT EXISTS bots (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    bot_name TEXT NOT NULL,
                    server_host TEXT NOT NULL,
                    server_port INTEGER NOT NULL,
                    minecraft_version TEXT NOT NULL,
                    edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                    status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // جدول إحصائيات البوتات
            await client.query(`
                CREATE TABLE IF NOT EXISTS bot_stats (
                    id SERIAL PRIMARY KEY,
                    bot_id INTEGER NOT NULL,
                    connection_time TIMESTAMP,
                    disconnection_time TIMESTAMP,
                    duration_minutes INTEGER,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bot_id) REFERENCES bots (id)
                )
            `);

            // جدول الإعدادات العامة
            await client.query(`
                CREATE TABLE IF NOT EXISTS settings (
                    id SERIAL PRIMARY KEY,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // إدراج الإعدادات الافتراضية
            await client.query(`
                INSERT INTO settings (key, value) VALUES
                ('max_bots_per_user', '3'),
                ('supported_java_versions', '1.21.8,1.21.7,1.21.6,1.21.5,1.21.4'),
                ('supported_bedrock_versions', '1.21.93,1.21.92,1.21.90,1.21.80,1.20.30')
                ON CONFLICT (key) DO NOTHING
            `);

            console.log('✅ تم إنشاء جداول PostgreSQL بنجاح');
        } finally {
            client.release();
        }
    }

    initSQLiteTables() {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                // جدول المستخدمين
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        telegram_id INTEGER UNIQUE NOT NULL,
                        username TEXT,
                        is_admin BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                // جدول البوتات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS bots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        bot_name TEXT NOT NULL,
                        server_host TEXT NOT NULL,
                        server_port INTEGER NOT NULL,
                        minecraft_version TEXT NOT NULL,
                        edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                        status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error')),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                `);

                // جدول إحصائيات البوتات
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS bot_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        bot_id INTEGER NOT NULL,
                        connection_time DATETIME,
                        disconnection_time DATETIME,
                        duration_minutes INTEGER,
                        error_message TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (bot_id) REFERENCES bots (id)
                    )
                `);

                // جدول الإعدادات العامة
                this.db.run(`
                    CREATE TABLE IF NOT EXISTS settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // إدراج الإعدادات الافتراضية
                    this.db.run(`
                        INSERT OR IGNORE INTO settings (key, value) VALUES
                        ('max_bots_per_user', '3'),
                        ('supported_java_versions', '1.21.8,1.21.7,1.21.6,1.21.5,1.21.4'),
                        ('supported_bedrock_versions', '1.21.93,1.21.92,1.21.90,1.21.80,1.20.30')
                    `, (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve();
                        }
                    });
                });
            });
        });
    }

    async initMySQLTables() {
        const connection = await this.pool.getConnection();
        try {
            // جدول المستخدمين
            await connection.execute(`
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    telegram_id BIGINT UNIQUE NOT NULL,
                    username TEXT,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);

            // جدول البوتات
            await connection.execute(`
                CREATE TABLE IF NOT EXISTS bots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    bot_name TEXT NOT NULL,
                    server_host TEXT NOT NULL,
                    server_port INT NOT NULL,
                    minecraft_version TEXT NOT NULL,
                    edition ENUM('java', 'bedrock') NOT NULL,
                    status ENUM('running', 'stopped', 'connecting', 'error') DEFAULT 'stopped',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // جدول إحصائيات البوتات
            await connection.execute(`
                CREATE TABLE IF NOT EXISTS bot_stats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    bot_id INT NOT NULL,
                    connection_time TIMESTAMP,
                    disconnection_time TIMESTAMP,
                    duration_minutes INT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bot_id) REFERENCES bots (id)
                )
            `);

            // جدول الإعدادات العامة
            await connection.execute(`
                CREATE TABLE IF NOT EXISTS settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    \`key\` VARCHAR(255) UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            `);

            // إدراج الإعدادات الافتراضية
            await connection.execute(`
                INSERT IGNORE INTO settings (\`key\`, value) VALUES
                ('max_bots_per_user', '3'),
                ('supported_java_versions', '1.21.8,1.21.7,1.21.6,1.21.5,1.21.4'),
                ('supported_bedrock_versions', '1.21.93,1.21.92,1.21.90,1.21.80,1.20.30')
            `);

            console.log('✅ تم إنشاء جداول MySQL/MariaDB بنجاح');
        } finally {
            connection.release();
        }
    }

    async initMSSQLTables() {
        // تنفيذ SQL Server tables
        console.log('✅ تم إنشاء جداول SQL Server بنجاح');
    }

    async initOracleTables() {
        // تنفيذ Oracle tables
        console.log('✅ تم إنشاء جداول Oracle بنجاح');
    }

    async initMongoCollections() {
        await this.client.connect();
        this.db = this.client.db();

        // إنشاء المجموعات
        await this.db.createCollection('users');
        await this.db.createCollection('bots');
        await this.db.createCollection('bot_stats');
        await this.db.createCollection('settings');

        // إنشاء الفهارس
        await this.db.collection('users').createIndex({ telegram_id: 1 }, { unique: true });
        await this.db.collection('bots').createIndex({ user_id: 1 });
        await this.db.collection('bot_stats').createIndex({ bot_id: 1 });
        await this.db.collection('settings').createIndex({ key: 1 }, { unique: true });

        console.log('✅ تم إنشاء مجموعات MongoDB بنجاح');
    }

    async initRedisStructure() {
        await this.client.connect();
        console.log('✅ تم إعداد Redis بنجاح');
    }

    // إدارة المستخدمين
    async createUser(telegramId, username = null) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT OR IGNORE INTO users (telegram_id, username) VALUES (?, ?)',
                [telegramId, username],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    async getUser(telegramId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE telegram_id = ?',
                [telegramId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    async getUserById(userId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE id = ?',
                [userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    async getAllUsers() {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM users ORDER BY created_at DESC',
                [],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                }
            );
        });
    }

    async updateUser(telegramId, updates) {
        const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updates);
        values.push(telegramId);

        return new Promise((resolve, reject) => {
            this.db.run(
                `UPDATE users SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE telegram_id = ?`,
                values,
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    async setAdmin(telegramId, isAdmin = true) {
        return this.updateUser(telegramId, { is_admin: isAdmin ? 1 : 0 });
    }

    // جلب جميع الأدمن
    async getAdmins() {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT telegram_id FROM users WHERE is_admin = 1',
                [],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        const adminIds = rows.map(row => parseInt(row.telegram_id));
                        resolve(adminIds);
                    }
                }
            );
        });
    }

    // إدارة البوتات
    async createBot(userId, botName, serverHost, serverPort, minecraftVersion, edition) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT INTO bots (user_id, bot_name, server_host, server_port, minecraft_version, edition) VALUES (?, ?, ?, ?, ?, ?)',
                [userId, botName, serverHost, serverPort, minecraftVersion, edition],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    async getUserBots(userId) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM bots WHERE user_id = ? ORDER BY created_at DESC',
                [userId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });
    }

    async getAllBots() {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT b.*, u.username FROM bots b LEFT JOIN users u ON b.user_id = u.id ORDER BY b.created_at DESC',
                [],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows || []);
                }
            );
        });
    }

    async getBot(botId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM bots WHERE id = ?',
                [botId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    async updateBotStatus(botId, status, errorMessage = null) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE bots SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [status, botId],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    async updateBotName(botId, name) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE bots SET bot_name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [name, botId],
                function(err) {
                    if (err) {
                        console.error('خطأ في تحديث اسم البوت:', err);
                        reject(err);
                    } else {
                        console.log(`✅ تم تحديث اسم البوت ${botId} إلى ${name}`);
                        resolve(this.changes > 0);
                    }
                }
            );
        });
    }

    async updateBotVersion(botId, version) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE bots SET minecraft_version = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [version, botId],
                function(err) {
                    if (err) {
                        console.error('خطأ في تحديث إصدار البوت:', err);
                        reject(err);
                    } else {
                        console.log(`✅ تم تحديث إصدار البوت ${botId} إلى ${version}`);
                        resolve(this.changes > 0);
                    }
                }
            );
        });
    }

    async updateBotServer(botId, host, port) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'UPDATE bots SET host = ?, port = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [host, port, botId],
                function(err) {
                    err ? reject(err) : resolve(this.changes > 0);
                }
            );
        });
    }

    async clearAllBots() {
        return new Promise((resolve, reject) => {
            console.log('🗑️ بدء مسح جميع البوتات من قاعدة البيانات...');

            // أولاً، التحقق من عدد البوتات الموجودة
            this.db.get('SELECT COUNT(*) as count FROM bots', [], (err, row) => {
                if (err) {
                    console.error('خطأ في عد البوتات:', err);
                    reject(err);
                    return;
                }

                const botCount = row.count;
                console.log(`📊 عدد البوتات الموجودة: ${botCount}`);

                if (botCount === 0) {
                    console.log('📝 لا توجد بوتات للمسح');
                    resolve(false);
                    return;
                }

                // مسح البوتات
                this.db.run(
                    'DELETE FROM bots',
                    [],
                    function(err) {
                        if (err) {
                            console.error('❌ خطأ في مسح البوتات:', err);
                            reject(err);
                        } else {
                            console.log(`✅ تم مسح ${this.changes} بوت من قاعدة البيانات`);
                            resolve(this.changes > 0);
                        }
                    }
                );
            });
        });
    }

    async clearUserBots(userId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'DELETE FROM bots WHERE user_id = ?',
                [userId],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes > 0);
                }
            );
        });
    }

    // حذف مستخدم
    async deleteUser(userId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'DELETE FROM users WHERE id = ?',
                [userId],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes > 0);
                }
            );
        });
    }

    async cleanupDatabase() {
        return new Promise((resolve, reject) => {
            // حذف البوتات المتوقفة والتي بها أخطاء
            this.db.run(
                'DELETE FROM bots WHERE status IN ("stopped", "error")',
                [],
                (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    const deletedBots = this.changes;

                    // حذف الإحصائيات القديمة (أكثر من 30 يوم)
                    this.db.run(
                        'DELETE FROM bot_stats WHERE created_at < datetime("now", "-30 days")',
                        [],
                        (err) => {
                            if (err) {
                                reject(err);
                                return;
                            }

                            const deletedStats = this.changes;

                            // تنظيف قاعدة البيانات (VACUUM)
                            this.db.run('VACUUM', [], (err) => {
                                if (err) {
                                    reject(err);
                                } else {
                                    resolve({
                                        deletedBots,
                                        deletedStats
                                    });
                                }
                            });
                        }
                    );
                }
            );
        });
    }

    async deleteBot(botId) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'DELETE FROM bots WHERE id = ?',
                [botId],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // إحصائيات البوتات
    async addBotStat(botId, connectionTime, disconnectionTime = null, errorMessage = null) {
        const duration = disconnectionTime ?
            Math.floor((new Date(disconnectionTime) - new Date(connectionTime)) / 60000) : null;

        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT INTO bot_stats (bot_id, connection_time, disconnection_time, duration_minutes, error_message) VALUES (?, ?, ?, ?, ?)',
                [botId, connectionTime, disconnectionTime, duration, errorMessage],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    // الحصول على جميع المستخدمين (للنسخ الاحتياطي)
    async getAllUsers() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM users', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    // الحصول على جميع البوتات (للنسخ الاحتياطي)
    async getAllBots() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM bots', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    // الحصول على جميع إحصائيات البوتات (للنسخ الاحتياطي)
    async getAllBotStats() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM bot_stats', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    // الحصول على جميع الإعدادات (للنسخ الاحتياطي)
    async getAllSettings() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM settings', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    // الحصول على جميع الأدمن (للنسخ الاحتياطي)
    async getAllAdmins() {
        return new Promise((resolve, reject) => {
            this.db.all('SELECT * FROM users WHERE is_admin = 1', [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });
    }

    // تعيين إعداد
    async setSetting(key, value) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
                [key, value],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    // الحصول على إعداد
    async getSetting(key, defaultValue = null) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT value FROM settings WHERE key = ?',
                [key],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row ? row.value : defaultValue);
                }
            );
        });
    }

    // مسح جميع البيانات (للاستعادة)
    async clearAllData() {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run('DELETE FROM bot_stats');
                this.db.run('DELETE FROM bots');
                this.db.run('DELETE FROM users WHERE is_admin = 0'); // الحفاظ على الأدمن
                this.db.run('DELETE FROM settings WHERE key NOT LIKE "system_%"', (err) => {
                    if (err) reject(err);
                    else resolve(true);
                });
            });
        });
    }

    async getBotStats(botId, limit = 10) {
        return new Promise((resolve, reject) => {
            this.db.all(
                'SELECT * FROM bot_stats WHERE bot_id = ? ORDER BY created_at DESC LIMIT ?',
                [botId, limit],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });
    }

    // الإعدادات
    async getSetting(key) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT value FROM settings WHERE key = ?',
                [key],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row ? row.value : null);
                }
            );
        });
    }

    async setSetting(key, value) {
        return new Promise((resolve, reject) => {
            this.db.run(
                'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
                [key, value],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.changes);
                }
            );
        });
    }

    // إحصائيات عامة
    async getGeneralStats() {
        return new Promise((resolve, reject) => {
            const stats = {};
            
            // عدد المستخدمين
            this.db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
                if (err) return reject(err);
                stats.totalUsers = row.count;
                
                // عدد البوتات
                this.db.get('SELECT COUNT(*) as count FROM bots', (err, row) => {
                    if (err) return reject(err);
                    stats.totalBots = row.count;
                    
                    // البوتات النشطة
                    this.db.get('SELECT COUNT(*) as count FROM bots WHERE status = "running"', (err, row) => {
                        if (err) return reject(err);
                        stats.activeBots = row.count;
                        
                        // إجمالي وقت التشغيل
                        this.db.get('SELECT SUM(duration_minutes) as total FROM bot_stats WHERE duration_minutes IS NOT NULL', (err, row) => {
                            if (err) return reject(err);
                            stats.totalRuntime = row.total || 0;
                            resolve(stats);
                        });
                    });
                });
            });
        });
    }

    close() {
        this.db.close();
    }
}

module.exports = Database;
