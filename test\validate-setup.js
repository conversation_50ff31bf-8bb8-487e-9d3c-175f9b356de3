#!/usr/bin/env node

/**
 * سكريبت للتحقق من صحة إعداد المشروع قبل النشر
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

class SetupValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.passed = [];
    }

    // تشغيل جميع الفحوصات
    async validate() {
        console.log('🔍 بدء التحقق من صحة الإعداد...\n');

        await this.checkEnvironmentVariables();
        await this.checkFiles();
        await this.checkDependencies();
        await this.checkConfiguration();
        await this.checkDatabase();
        await this.checkSecurity();

        this.printResults();
        
        if (this.errors.length > 0) {
            process.exit(1);
        }
    }

    // فحص متغيرات البيئة
    async checkEnvironmentVariables() {
        console.log('📋 فحص متغيرات البيئة...');

        const required = [
            'TELEGRAM_BOT_TOKEN'
        ];

        const recommended = [
            'ADMIN_IDS',
            'NODE_ENV',
            'DATABASE_URL',
            'MAX_BOTS_PER_USER'
        ];

        // فحص المتغيرات المطلوبة
        for (const variable of required) {
            if (!process.env[variable]) {
                this.errors.push(`متغير البيئة المطلوب غير موجود: ${variable}`);
            } else if (process.env[variable] === 'your_token_here' || 
                      process.env[variable] === 'YOUR_BOT_TOKEN_HERE') {
                this.errors.push(`متغير البيئة يحتوي على قيمة افتراضية: ${variable}`);
            } else {
                this.passed.push(`متغير البيئة المطلوب موجود: ${variable}`);
            }
        }

        // فحص المتغيرات المستحسنة
        for (const variable of recommended) {
            if (!process.env[variable]) {
                this.warnings.push(`متغير البيئة المستحسن غير موجود: ${variable}`);
            } else {
                this.passed.push(`متغير البيئة المستحسن موجود: ${variable}`);
            }
        }

        // فحص صحة توكن التلغرام
        if (process.env.TELEGRAM_BOT_TOKEN) {
            const token = process.env.TELEGRAM_BOT_TOKEN;
            if (!token.includes(':') || token.length < 40) {
                this.errors.push('توكن التلغرام غير صحيح الشكل');
            } else {
                this.passed.push('توكن التلغرام صحيح الشكل');
            }
        }

        // فحص معرفات الأدمن
        if (process.env.ADMIN_IDS) {
            const adminIds = process.env.ADMIN_IDS.split(',');
            const validIds = adminIds.filter(id => !isNaN(parseInt(id.trim())));
            if (validIds.length === 0) {
                this.warnings.push('لا توجد معرفات أدمن صحيحة');
            } else {
                this.passed.push(`تم العثور على ${validIds.length} معرف أدمن صحيح`);
            }
        }
    }

    // فحص الملفات المطلوبة
    async checkFiles() {
        console.log('📁 فحص الملفات المطلوبة...');

        const requiredFiles = [
            'package.json',
            'index.js',
            'config.js',
            'telegram-bot.js',
            'bot-manager.js',
            'database.js',
            'security.js',
            'health-check.js'
        ];

        const recommendedFiles = [
            '.env.example',
            'README.md',
            'DEPLOYMENT.md',
            'Dockerfile',
            'railway.toml',
            '.gitignore'
        ];

        // فحص الملفات المطلوبة
        for (const file of requiredFiles) {
            if (fs.existsSync(file)) {
                this.passed.push(`الملف المطلوب موجود: ${file}`);
            } else {
                this.errors.push(`الملف المطلوب غير موجود: ${file}`);
            }
        }

        // فحص الملفات المستحسنة
        for (const file of recommendedFiles) {
            if (fs.existsSync(file)) {
                this.passed.push(`الملف المستحسن موجود: ${file}`);
            } else {
                this.warnings.push(`الملف المستحسن غير موجود: ${file}`);
            }
        }
    }

    // فحص التبعيات
    async checkDependencies() {
        console.log('📦 فحص التبعيات...');

        try {
            const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
            
            const requiredDeps = [
                'dotenv',
                'node-telegram-bot-api',
                'mineflayer',
                'bedrock-protocol',
                'sqlite3',
                'uuid'
            ];

            for (const dep of requiredDeps) {
                if (packageJson.dependencies && packageJson.dependencies[dep]) {
                    this.passed.push(`التبعية المطلوبة موجودة: ${dep}`);
                } else {
                    this.errors.push(`التبعية المطلوبة غير موجودة: ${dep}`);
                }
            }

            // فحص إصدار Node.js
            if (packageJson.engines && packageJson.engines.node) {
                this.passed.push(`إصدار Node.js محدد: ${packageJson.engines.node}`);
            } else {
                this.warnings.push('إصدار Node.js غير محدد في package.json');
            }

        } catch (error) {
            this.errors.push(`خطأ في قراءة package.json: ${error.message}`);
        }
    }

    // فحص الإعدادات
    async checkConfiguration() {
        console.log('⚙️ فحص الإعدادات...');

        try {
            const { config, validateConfig } = require('../config');
            
            const validation = validateConfig();
            if (validation.valid) {
                this.passed.push('إعدادات النظام صحيحة');
            } else {
                for (const error of validation.errors) {
                    this.errors.push(`خطأ في الإعدادات: ${error}`);
                }
            }

            // فحص الإصدارات المدعومة
            if (config.supportedVersions.java.length > 0) {
                this.passed.push(`إصدارات Java المدعومة: ${config.supportedVersions.java.length}`);
            } else {
                this.errors.push('لا توجد إصدارات Java مدعومة');
            }

            if (config.supportedVersions.bedrock.length > 0) {
                this.passed.push(`إصدارات Bedrock المدعومة: ${config.supportedVersions.bedrock.length}`);
            } else {
                this.errors.push('لا توجد إصدارات Bedrock مدعومة');
            }

        } catch (error) {
            this.errors.push(`خطأ في تحميل الإعدادات: ${error.message}`);
        }
    }

    // فحص قاعدة البيانات
    async checkDatabase() {
        console.log('🗄️ فحص قاعدة البيانات...');

        try {
            const Database = require('../database');
            const db = new Database();
            await db.init();
            this.passed.push('قاعدة البيانات تعمل بشكل صحيح');
        } catch (error) {
            this.errors.push(`خطأ في قاعدة البيانات: ${error.message}`);
        }
    }

    // فحص الأمان
    async checkSecurity() {
        console.log('🔒 فحص الأمان...');

        // فحص ملف .env في .gitignore
        if (fs.existsSync('.gitignore')) {
            const gitignore = fs.readFileSync('.gitignore', 'utf8');
            if (gitignore.includes('.env')) {
                this.passed.push('ملف .env مستثنى من Git');
            } else {
                this.warnings.push('ملف .env غير مستثنى من Git');
            }
        }

        // فحص وجود ملفات حساسة
        const sensitiveFiles = ['.env', 'secrets.js', 'config.local.js'];
        for (const file of sensitiveFiles) {
            if (fs.existsSync(file)) {
                this.warnings.push(`ملف حساس موجود: ${file} - تأكد من عدم رفعه للمستودع`);
            }
        }

        // فحص إعدادات الأمان
        try {
            const securityManager = require('../security');
            const stats = securityManager.getSecurityStats();
            this.passed.push('مدير الأمان يعمل بشكل صحيح');
        } catch (error) {
            this.warnings.push(`تحذير في مدير الأمان: ${error.message}`);
        }
    }

    // طباعة النتائج
    printResults() {
        console.log('\n📊 نتائج التحقق:\n');

        if (this.passed.length > 0) {
            console.log('✅ نجح:');
            for (const item of this.passed) {
                console.log(`   ✓ ${item}`);
            }
            console.log('');
        }

        if (this.warnings.length > 0) {
            console.log('⚠️ تحذيرات:');
            for (const item of this.warnings) {
                console.log(`   ⚠ ${item}`);
            }
            console.log('');
        }

        if (this.errors.length > 0) {
            console.log('❌ أخطاء:');
            for (const item of this.errors) {
                console.log(`   ✗ ${item}`);
            }
            console.log('');
        }

        console.log('📈 الملخص:');
        console.log(`   ✅ نجح: ${this.passed.length}`);
        console.log(`   ⚠️ تحذيرات: ${this.warnings.length}`);
        console.log(`   ❌ أخطاء: ${this.errors.length}`);

        if (this.errors.length === 0) {
            console.log('\n🎉 المشروع جاهز للنشر!');
        } else {
            console.log('\n🚫 يجب إصلاح الأخطاء قبل النشر');
        }
    }
}

// تشغيل التحقق إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const validator = new SetupValidator();
    validator.validate().catch(error => {
        console.error('❌ خطأ في التحقق:', error.message);
        process.exit(1);
    });
}

module.exports = SetupValidator;
