#!/bin/bash
# ملف التشغيل السريع للنظام المحسن
# Enhanced System Quick Start Script

echo "🚀 بدء تشغيل نظام بوت ماينكرافت المحسن..."
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً."
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت. يرجى تثبيت npm أولاً."
    exit 1
fi

echo "✅ Node.js و npm متوفران"

# تثبيت التبعيات إذا لم تكن موجودة
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت التبعيات..."
    npm install
fi

# التحقق من ملف الإعدادات
if [ ! -f ".env" ]; then
    if [ -f ".env.enhanced" ]; then
        echo "📋 نسخ إعدادات النظام المحسن..."
        cp .env.enhanced .env
    else
        echo "⚠️ ملف الإعدادات غير موجود. يرجى إنشاء ملف .env"
        echo "💡 يمكنك استخدام .env.enhanced كنموذج"
        exit 1
    fi
fi

echo "🔧 التحقق من صحة الإعدادات..."
node -e "
try {
    const {config, validateConfig} = require('./config');
    const validation = validateConfig();
    if (validation.valid) {
        console.log('✅ جميع الإعدادات صحيحة');
    } else {
        console.log('❌ مشاكل في الإعدادات:');
        validation.errors.forEach(error => console.log('  -', error));
        process.exit(1);
    }
} catch (error) {
    console.log('❌ خطأ في تحميل الإعدادات:', error.message);
    process.exit(1);
}
"

if [ $? -ne 0 ]; then
    echo "❌ فشل في التحقق من الإعدادات"
    exit 1
fi

echo ""
echo "🎯 الميزات المحسنة:"
echo "  • دعم آخر 5 إصدارات Java وآخر 4 إصدارات Bedrock + 1.20.30"
echo "  • إعادة اتصال كل 20 ثانية لمدة 5 دقائق"
echo "  • إشعارات محسنة ومفصلة"
echo "  • إشعار فوري عند دخول العالم"
echo "  • نظام مراقبة محسن"
echo ""

echo "🚀 بدء تشغيل النظام..."
npm start
