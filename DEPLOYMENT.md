# 🚀 دليل النشر على Railway.com

دليل شامل لنشر نظام بوت ماينكرافت على Railway.com

## 📋 المتطلبات المسبقة

- حساب على [Railway.com](https://railway.app)
- حساب على [GitHub](https://github.com)
- توكن بوت التلغرام من [BotFather](https://t.me/BotFather)

## 🔧 إعداد المشروع

### 1. إعداد مستودع GitHub

```bash
# إنشاء مستودع جديد على GitHub
# ثم استنساخه محلياً
git clone https://github.com/your-username/minecraft-telegram-bot-system.git
cd minecraft-telegram-bot-system

# رفع الكود
git add .
git commit -m "Initial commit"
git push origin main
```

### 2. إنشاء بوت التلغرام

1. ابدأ محادثة مع [@BotFather](https://t.me/BotFather)
2. استخدم الأمر `/newbot`
3. اتبع التعليمات لإنشاء البوت
4. احفظ التوكن الذي ستحصل عليه

### 3. الحصول على معرف التلغرام

1. ابدأ محادثة مع [@userinfobot](https://t.me/userinfobot)
2. احفظ معرفك (User ID)

## 🌐 النشر على Railway

### 1. إنشاء مشروع جديد

1. اذهب إلى [Railway.com](https://railway.app)
2. سجل الدخول أو أنشئ حساب جديد
3. انقر على "New Project"
4. اختر "Deploy from GitHub repo"
5. اختر مستودعك

### 2. تعيين متغيرات البيئة

في لوحة تحكم Railway، اذهب إلى تبويب "Variables" وأضف:

#### المتغيرات الأساسية (مطلوبة)
```
TELEGRAM_BOT_TOKEN=your_bot_token_here
ADMIN_IDS=your_telegram_user_id
NODE_ENV=production
```

#### متغيرات الأداء (اختيارية)
```
MAX_BOTS_PER_USER=3
RATE_LIMIT_WINDOW=60000
MAX_REQUESTS_PER_WINDOW=30
CONNECTION_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=30000
RESPONSE_TIMEOUT=10000
```

#### متغيرات المراقبة (اختيارية)
```
MONITORING_ENABLED=true
MONITORING_INTERVAL=60000
PERFORMANCE_MONITORING=true
ALERTS_ENABLED=true
MEMORY_THRESHOLD=80
ERROR_RATE_THRESHOLD=10
```

#### متغيرات السجلات (اختيارية)
```
LOG_LEVEL=info
LOG_TO_FILE=false
```

### 3. إعداد قاعدة البيانات (اختياري)

#### استخدام PostgreSQL على Railway:

1. في مشروعك على Railway، انقر على "New"
2. اختر "Database" ثم "PostgreSQL"
3. انتظر حتى يتم إنشاء قاعدة البيانات
4. انسخ رابط الاتصال (DATABASE_URL)
5. أضف المتغير:
```
DATABASE_URL=postgresql://username:password@host:port/database
```

#### استخدام SQLite (افتراضي):
لا حاجة لإعداد إضافي، سيتم استخدام SQLite تلقائياً.

### 4. النشر

1. بعد تعيين المتغيرات، سيبدأ النشر تلقائياً
2. راقب سجلات النشر في تبويب "Deployments"
3. تأكد من عدم وجود أخطاء

### 5. التحقق من النشر

1. اذهب إلى تبويب "Logs" لمراقبة السجلات
2. ابحث عن رسالة "✅ تم تشغيل النظام بنجاح!"
3. اختبر البوت عبر إرسال `/start` في التلغرام

## 🔧 إعدادات متقدمة

### تخصيص الموارد

في ملف `railway.toml`:
```toml
[resources]
memoryLimit = "512Mi"  # حد الذاكرة
cpuLimit = "500m"      # حد المعالج
```

### إعداد النطاق المخصص

1. في لوحة تحكم Railway، اذهب إلى "Settings"
2. في قسم "Domains"، أضف نطاقك المخصص
3. اتبع التعليمات لإعداد DNS

### تفعيل HTTPS

```toml
[security]
forceHttps = true
```

## 📊 المراقبة والصيانة

### مراقبة الأداء

1. استخدم تبويب "Metrics" في Railway
2. راقب استخدام الذاكرة والمعالج
3. تحقق من سجلات الأخطاء

### النسخ الاحتياطي

#### لقاعدة بيانات PostgreSQL:
```bash
# تصدير قاعدة البيانات
pg_dump $DATABASE_URL > backup.sql

# استيراد قاعدة البيانات
psql $DATABASE_URL < backup.sql
```

#### لقاعدة بيانات SQLite:
النسخ الاحتياطي يتم تلقائياً في مجلد `backups/`

### التحديثات

1. ادفع التغييرات إلى GitHub
2. سيتم النشر تلقائياً على Railway
3. راقب سجلات النشر للتأكد من النجاح

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ "Application failed to respond"
- تحقق من متغير `PORT`
- تأكد من أن التطبيق يستمع على المنفذ الصحيح

#### خطأ "Out of memory"
- زد حد الذاكرة في `railway.toml`
- قلل من `MAX_BOTS_PER_USER`

#### خطأ "Database connection failed"
- تحقق من صحة `DATABASE_URL`
- تأكد من أن قاعدة البيانات تعمل

#### خطأ "Telegram polling error"
- تحقق من صحة `TELEGRAM_BOT_TOKEN`
- تأكد من أن البوت لم يتم حذفه

### فحص السجلات

```bash
# عرض السجلات المباشرة
railway logs

# عرض السجلات السابقة
railway logs --tail 100
```

### إعادة التشغيل

```bash
# إعادة تشغيل الخدمة
railway restart
```

## 🔒 الأمان

### حماية متغيرات البيئة

- لا تشارك متغيرات البيئة الحساسة
- استخدم أسماء متغيرات واضحة
- راجع الصلاحيات بانتظام

### مراقبة الأمان

- فعل `ALERTS_ENABLED=true`
- راقب محاولات الوصول المشبوهة
- حدث كلمات المرور بانتظام

## 📈 تحسين الأداء

### تحسين الذاكرة

```
MAX_MEMORY_USAGE=536870912  # 512MB
GC_INTERVAL=300000          # 5 دقائق
```

### تحسين الشبكة

```
CONNECTION_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=30000
RESPONSE_TIMEOUT=10000
```

### تحسين قاعدة البيانات

```
VACUUM_INTERVAL=604800000   # أسبوع
ANALYZE_INTERVAL=86400000   # يوم
CHECKPOINT_INTERVAL=3600000 # ساعة
```

## 📞 الدعم

إذا واجهت مشاكل:

1. تحقق من [الوثائق](README.md)
2. ابحث في [GitHub Issues](https://github.com/your-username/minecraft-telegram-bot-system/issues)
3. أنشئ issue جديد مع تفاصيل المشكلة
4. تواصل مع فريق الدعم

## 🎯 نصائح للنجاح

1. **ابدأ بالإعدادات الأساسية** ثم أضف المتقدمة
2. **راقب السجلات** باستمرار في البداية
3. **اختبر البوت** بعد كل تحديث
4. **احتفظ بنسخ احتياطية** من الإعدادات المهمة
5. **وثق التغييرات** التي تقوم بها

---

<div align="center">
  <strong>نشر ناجح! 🎉</strong>
</div>
