require('dotenv').config();
console.log('🔧 تم تحميل متغيرات البيئة');

const MinecraftTelegramBot = require('./telegram-bot');

// توكن بوت التلغرام من متغيرات البيئة
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

// معرفات الأدمن من متغيرات البيئة
const ADMIN_IDS = process.env.ADMIN_IDS ?
    process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) :
    [];

async function startSystem() {
    try {
        console.log('🚀 بدء تشغيل نظام بوت ماينكرافت...');
        console.log('📋 التحقق من متغيرات البيئة...');

        // التحقق من وجود التوكن
        if (!TELEGRAM_BOT_TOKEN || TELEGRAM_BOT_TOKEN === 'YOUR_BOT_TOKEN_HERE') {
            console.error('❌ توكن التلغرام غير موجود أو غير صحيح');
            console.error('💡 يرجى تعديل ملف .env وإضافة TELEGRAM_BOT_TOKEN');
            process.exit(1);
        }

        console.log('✅ توكن التلغرام موجود');
        console.log('✅ معرفات الأدمن:', ADMIN_IDS);

        // إنشاء وتهيئة بوت التلغرام
        console.log('🤖 إنشاء بوت التلغرام...');
        const telegramBot = new MinecraftTelegramBot(TELEGRAM_BOT_TOKEN);
        
        console.log('🔧 تهيئة البوت...');
        await telegramBot.init();
        
        console.log('✅ تم تهيئة بوت التلغرام بنجاح');
        console.log('📱 بوت التلغرام جاهز للاستخدام');
        console.log('🎮 يمكن الآن إنشاء بوتات ماينكرافت Java و Bedrock');

        console.log('\n📋 الميزات المتاحة:');
        console.log('   • إنشاء بوتات ماينكرافت (Java & Bedrock)');
        console.log('   • دعم أحدث الإصدارات');
        console.log('   • التحكم الكامل في البوتات (تشغيل/إيقاف)');
        console.log('   • إرسال الرسائل والأوامر');
        console.log('   • مراقبة الإحصائيات');
        console.log('   • واجهة إدارة للأدمن');

        console.log('\n🎯 نظام التحذيرات المحدث:');
        console.log('   • إعادة الاتصال: كل 20 ثانية لمدة 5 دقائق (15 محاولة)');
        console.log('   • التحذيرات: أول رسالة فوراً، ثم كل دقيقة (5 رسائل إجمالية)');
        console.log('   • إشعار فوري عند دخول البوت للعالم');
        console.log('   • إشعار نهائي عند إيقاف البوت بعد 5 دقائق');

        console.log('\n🔧 للحصول على المساعدة، استخدم الأمر /help في بوت التلغرام');
        console.log('✅ تم تشغيل النظام بنجاح!');

        // إبقاء العملية قيد التشغيل
        process.on('SIGINT', () => {
            console.log('\n🛑 إيقاف النظام...');
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 إيقاف النظام...');
            process.exit(0);
        });

        // رسالة دورية للتأكد من أن النظام يعمل
        setInterval(() => {
            console.log(`⏰ النظام يعمل - ${new Date().toLocaleString('ar-SA')}`);
        }, 300000); // كل 5 دقائق

    } catch (error) {
        console.error('❌ خطأ في تشغيل النظام:', error.message);
        console.error('🔧 تفاصيل الخطأ:', error.stack);
        
        console.log('\n💡 نصائح لحل المشكلة:');
        console.log('   1. تأكد من صحة توكن التلغرام في ملف .env');
        console.log('   2. تأكد من تثبيت جميع التبعيات: npm install');
        console.log('   3. تأكد من وجود ملف قاعدة البيانات');
        console.log('   4. تحقق من الاتصال بالإنترنت');
        
        process.exit(1);
    }
}

// تشغيل النظام
startSystem();
