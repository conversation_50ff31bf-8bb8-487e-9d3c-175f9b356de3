#!/usr/bin/env node

/**
 * اختبار نظام النسخ الاحتياطي
 */

require('dotenv').config();
const BackupManager = require('./backup-manager');
const Database = require('./database');
const fs = require('fs').promises;
const path = require('path');

class BackupTester {
    constructor() {
        this.db = null;
        this.backupManager = null;
        this.testResults = [];
    }

    async init() {
        console.log('🔄 تهيئة نظام الاختبار...');
        
        this.db = await new Database().init();
        this.backupManager = new BackupManager(this.db, null);
        
        console.log('✅ تم تهيئة النظام بنجاح');
    }

    async runAllTests() {
        console.log('🧪 بدء اختبار نظام النسخ الاحتياطي...\n');

        await this.init();

        // اختبار إنشاء النسخة الاحتياطية
        await this.testCreateBackup();
        
        // اختبار قائمة النسخ الاحتياطية
        await this.testListBackups();
        
        // اختبار حالة النسخ الاحتياطي
        await this.testBackupStatus();
        
        // اختبار النسخ الاحتياطي التلقائي
        await this.testAutoBackup();
        
        // اختبار استعادة النسخة الاحتياطية
        await this.testRestoreBackup();

        this.printResults();
    }

    async testCreateBackup() {
        console.log('1️⃣ اختبار إنشاء النسخة الاحتياطية...');
        
        try {
            const backup = await this.backupManager.createBackup(true);
            
            if (backup.success) {
                console.log(`   ✅ تم إنشاء النسخة الاحتياطية: ${backup.fileName}`);
                console.log(`   📦 حجم الملف: ${this.backupManager.formatFileSize(backup.size)}`);
                
                // التحقق من وجود الملف
                const fileExists = await this.fileExists(backup.filePath);
                if (fileExists) {
                    console.log('   ✅ الملف موجود على القرص');
                    this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'نجح' });
                } else {
                    console.log('   ❌ الملف غير موجود على القرص');
                    this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'فشل', error: 'الملف غير موجود' });
                }
            } else {
                console.log(`   ❌ فشل في إنشاء النسخة الاحتياطية: ${backup.error}`);
                this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'فشل', error: backup.error });
            }
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testListBackups() {
        console.log('2️⃣ اختبار قائمة النسخ الاحتياطية...');
        
        try {
            const backups = await this.backupManager.listBackups();
            
            console.log(`   📋 عدد النسخ الاحتياطية: ${backups.length}`);
            
            if (backups.length > 0) {
                const latestBackup = backups[0];
                console.log(`   📅 آخر نسخة: ${latestBackup.created.toLocaleString('ar-EG')}`);
                console.log(`   📦 حجم آخر نسخة: ${this.backupManager.formatFileSize(latestBackup.size)}`);
                
                this.testResults.push({ test: 'قائمة النسخ الاحتياطية', status: 'نجح' });
            } else {
                console.log('   ⚠️ لا توجد نسخ احتياطية');
                this.testResults.push({ test: 'قائمة النسخ الاحتياطية', status: 'تحذير', error: 'لا توجد نسخ' });
            }
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'قائمة النسخ الاحتياطية', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testBackupStatus() {
        console.log('3️⃣ اختبار حالة النسخ الاحتياطي...');
        
        try {
            const status = this.backupManager.getBackupStatus();
            
            console.log(`   🔄 النسخ التلقائي: ${status.autoBackupEnabled ? 'مفعل' : 'معطل'}`);
            console.log(`   ⏱️ الفترة الزمنية: ${status.intervalMinutes} دقيقة`);
            console.log(`   📁 مجلد النسخ: ${status.backupPath}`);
            console.log(`   🗜️ الضغط: ${status.compressionEnabled ? 'مفعل' : 'معطل'}`);
            console.log(`   🔒 التشفير: ${status.encryptionEnabled ? 'مفعل' : 'معطل'}`);
            
            this.testResults.push({ test: 'حالة النسخ الاحتياطي', status: 'نجح' });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'حالة النسخ الاحتياطي', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testAutoBackup() {
        console.log('4️⃣ اختبار النسخ الاحتياطي التلقائي...');
        
        try {
            // تشغيل النسخ التلقائي
            const startResult = this.backupManager.startAutoBackup();
            console.log(`   ▶️ تشغيل النسخ التلقائي: ${startResult ? 'نجح' : 'فشل'}`);
            
            // انتظار قصير
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // إيقاف النسخ التلقائي
            const stopResult = this.backupManager.stopAutoBackup();
            console.log(`   ⏹️ إيقاف النسخ التلقائي: ${stopResult ? 'نجح' : 'فشل'}`);
            
            if (startResult && stopResult) {
                this.testResults.push({ test: 'النسخ الاحتياطي التلقائي', status: 'نجح' });
            } else {
                this.testResults.push({ test: 'النسخ الاحتياطي التلقائي', status: 'فشل' });
            }
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'النسخ الاحتياطي التلقائي', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testRestoreBackup() {
        console.log('5️⃣ اختبار استعادة النسخة الاحتياطية...');
        
        try {
            const backups = await this.backupManager.listBackups();
            
            if (backups.length === 0) {
                console.log('   ⚠️ لا توجد نسخ احتياطية للاختبار');
                this.testResults.push({ test: 'استعادة النسخة الاحتياطية', status: 'تخطي', error: 'لا توجد نسخ' });
                return;
            }
            
            const latestBackup = backups[0];
            console.log(`   🔄 اختبار استعادة: ${latestBackup.name}`);
            
            // محاولة استعادة النسخة (بدون الكتابة الفعلية)
            const result = await this.backupManager.restoreBackup(latestBackup.path, true);
            
            if (result.success) {
                console.log('   ✅ تم اختبار الاستعادة بنجاح');
                console.log(`   📅 تاريخ النسخة: ${new Date(result.metadata.timestamp).toLocaleString('ar-EG')}`);
                console.log(`   👥 المستخدمون: ${result.restoredData.users}`);
                console.log(`   🤖 البوتات: ${result.restoredData.bots}`);
                
                this.testResults.push({ test: 'استعادة النسخة الاحتياطية', status: 'نجح' });
            } else {
                console.log(`   ❌ فشل في الاستعادة: ${result.error}`);
                this.testResults.push({ test: 'استعادة النسخة الاحتياطية', status: 'فشل', error: result.error });
            }
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'استعادة النسخة الاحتياطية', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    printResults() {
        console.log('📊 نتائج الاختبار:\n');
        
        let passed = 0;
        let failed = 0;
        let warnings = 0;
        let errors = 0;
        
        this.testResults.forEach((result, index) => {
            let statusIcon = '';
            switch (result.status) {
                case 'نجح':
                    statusIcon = '✅';
                    passed++;
                    break;
                case 'فشل':
                    statusIcon = '❌';
                    failed++;
                    break;
                case 'تحذير':
                    statusIcon = '⚠️';
                    warnings++;
                    break;
                case 'خطأ':
                    statusIcon = '💥';
                    errors++;
                    break;
                case 'تخطي':
                    statusIcon = '⏭️';
                    break;
            }
            
            console.log(`${index + 1}. ${statusIcon} ${result.test}: ${result.status}`);
            if (result.error) {
                console.log(`   📝 التفاصيل: ${result.error}`);
            }
        });
        
        console.log('\n📈 الملخص:');
        console.log(`   ✅ نجح: ${passed}`);
        console.log(`   ❌ فشل: ${failed}`);
        console.log(`   ⚠️ تحذيرات: ${warnings}`);
        console.log(`   💥 أخطاء: ${errors}`);
        
        if (failed === 0 && errors === 0) {
            console.log('\n🎉 جميع الاختبارات نجحت! نظام النسخ الاحتياطي جاهز للاستخدام.');
        } else {
            console.log('\n🚫 بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        }
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const tester = new BackupTester();
    tester.runAllTests().catch(error => {
        console.error('❌ خطأ في تشغيل الاختبارات:', error.message);
        process.exit(1);
    });
}

module.exports = BackupTester;
