require('dotenv').config();
console.log('🔧 تم تحميل متغيرات البيئة');
const MinecraftTelegramBot = require('./telegram-bot');
const healthChecker = require('./health-check');

// توكن بوت التلغرام من متغيرات البيئة
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

// معرفات الأدمن من متغيرات البيئة
const ADMIN_IDS = process.env.ADMIN_IDS ?
    process.env.ADMIN_IDS.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) :
    [];

class MinecraftBotSystem {
    constructor() {
        this.telegramBot = null;
        this.isRunning = false;
    }

    async start() {
        try {
            console.log('🚀 بدء تشغيل نظام بوت ماينكرافت...');
            console.log('📋 التحقق من متغيرات البيئة...');

            // بدء فحص الصحة أولاً (قبل أي شيء آخر) - معطل مؤقتاً
            // healthChecker.start();
            console.log('✅ تم بدء خادم فحص الصحة');

            // التحقق من وجود التوكن
            if (!TELEGRAM_BOT_TOKEN || TELEGRAM_BOT_TOKEN === 'YOUR_BOT_TOKEN_HERE') {
                console.warn('⚠️ توكن التلغرام غير موجود - سيعمل النظام في وضع محدود');
                this.isRunning = true;
                return;
            }

            // إنشاء وتهيئة بوت التلغرام
            this.telegramBot = new MinecraftTelegramBot(TELEGRAM_BOT_TOKEN);
            await this.telegramBot.init();
            console.log('✅ تم تهيئة بوت التلغرام بنجاح');

            // انتظار تهيئة البوت
            await new Promise(resolve => setTimeout(resolve, 2000));

            // إضافة الأدمن
            for (const adminId of ADMIN_IDS) {
                await this.telegramBot.addAdmin(adminId);
            }

            // تحميل الأدمن من قاعدة البيانات
            await this.telegramBot.loadAdmins();

            // بدء النسخ الاحتياطي التلقائي إذا كان مفعلاً
            if (process.env.AUTO_BACKUP_ENABLED === 'true') {
                const started = this.telegramBot.backupManager.startAutoBackup();
                if (started) {
                    console.log('🔄 تم تفعيل النسخ الاحتياطي التلقائي');
                }
            }

            this.isRunning = true;

            console.log('✅ تم تشغيل النظام بنجاح!');
            console.log('📱 بوت التلغرام جاهز للاستخدام');
            console.log('🎮 يمكن الآن إنشاء بوتات ماينكرافت Java و Bedrock');
            console.log('');
            console.log('📋 الميزات المتاحة:');
            console.log('   • إنشاء بوتات ماينكرافت (Java & Bedrock)');
            console.log('   • دعم آخر 5 إصدارات لكل نوع');
            console.log('   • التحكم الكامل في البوتات (تشغيل/إيقاف)');
            console.log('   • إرسال الرسائل والأوامر');
            console.log('   • مراقبة الإحصائيات');
            console.log('   • واجهة إدارة للأدمن');
            console.log('   • دعم سيرفرات Aternos وغيرها');
            console.log('');
            console.log('🔧 للحصول على المساعدة، استخدم الأمر /help في بوت التلغرام');

        } catch (error) {
            console.error('❌ خطأ في تشغيل النظام:', error.message);
            process.exit(1);
        }
    }

    async stop() {
        if (this.isRunning && this.telegramBot) {
            console.log('🔄 إيقاف النظام...');

            // إيقاف فحص الصحة
            healthChecker.stop();

            await this.telegramBot.shutdown();
            this.isRunning = false;
            console.log('✅ تم إيقاف النظام بنجاح');
        }
    }

    // معالجة إشارات النظام للإيقاف الآمن
    setupGracefulShutdown() {
        const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
        
        signals.forEach(signal => {
            process.on(signal, async () => {
                console.log(`\n📡 تم استقبال إشارة ${signal}`);
                await this.stop();
                process.exit(0);
            });
        });

        // معالجة الأخطاء غير المتوقعة
        process.on('uncaughtException', async (error) => {
            console.error('❌ خطأ غير متوقع:', error);
            await this.stop();
            process.exit(1);
        });

        process.on('unhandledRejection', async (reason, promise) => {
            // تجاهل أخطاء Telegram العادية
            if (reason && reason.code === 'ETELEGRAM') {
                console.log('تجاهل خطأ Telegram:', reason.message);
                return;
            }

            console.error('❌ Promise مرفوض:', reason);
            await this.stop();
            process.exit(1);
        });
    }
}

// إنشاء وتشغيل النظام
const system = new MinecraftBotSystem();

// إعداد الإيقاف الآمن
system.setupGracefulShutdown();

// بدء التشغيل
system.start().catch(error => {
    console.error('❌ فشل في تشغيل النظام:', error);
    process.exit(1);
});

// تصدير النظام للاستخدام الخارجي
module.exports = MinecraftBotSystem;
