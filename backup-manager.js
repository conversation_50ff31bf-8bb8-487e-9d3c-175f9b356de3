const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');
const { config } = require('./config');

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

class BackupManager {
    constructor(database, telegramBot) {
        this.db = database;
        this.telegramBot = telegramBot;
        this.backupPath = config.database.backup.path;
        this.autoBackupInterval = null;
        this.isAutoBackupEnabled = false;
        
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        this.ensureBackupDirectory();
    }

    // إنشاء مجلد النسخ الاحتياطي
    async ensureBackupDirectory() {
        try {
            await fs.mkdir(this.backupPath, { recursive: true });
            console.log(`📁 تم إنشاء مجلد النسخ الاحتياطي: ${this.backupPath}`);
        } catch (error) {
            console.error('خطأ في إنشاء مجلد النسخ الاحتياطي:', error);
        }
    }

    // إنشاء نسخة احتياطية شاملة
    async createBackup(includeSystemInfo = true) {
        try {
            console.log('🔄 بدء إنشاء النسخة الاحتياطية...');
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                metadata: {
                    version: '1.0.0',
                    timestamp: new Date().toISOString(),
                    type: 'full_backup',
                    creator: 'MinecraftBotSystem',
                    nodeVersion: process.version,
                    platform: process.platform
                },
                database: {},
                systemInfo: includeSystemInfo ? await this.getSystemInfo() : null,
                statistics: await this.getSystemStatistics()
            };

            // جمع بيانات قاعدة البيانات
            if (this.db) {
                backupData.database.users = await this.db.getAllUsers();
                backupData.database.bots = await this.db.getAllBots();
                backupData.database.botStats = await this.db.getAllBotStats();
                backupData.database.settings = await this.db.getAllSettings();
                backupData.database.admins = await this.db.getAllAdmins();
            } else {
                console.log('⚠️ قاعدة البيانات غير متوفرة، إنشاء نسخة احتياطية فارغة');
                backupData.database.users = [];
                backupData.database.bots = [];
                backupData.database.botStats = [];
                backupData.database.settings = [];
                backupData.database.admins = [];
            }

            // تحويل البيانات إلى JSON
            let jsonData = JSON.stringify(backupData, null, 2);
            
            // ضغط البيانات إذا كان مفعلاً
            if (config.database.backup.compression.enabled) {
                jsonData = await gzip(jsonData);
                console.log('🗜️ تم ضغط النسخة الاحتياطية');
            }

            // التشفير معطل - لا حاجة للتشفير

            // حفظ النسخة الاحتياطية (مضغوطة فقط، بدون تشفير)
            const fileName = `backup_${timestamp}.json${config.database.backup.compression.enabled ? '.gz' : ''}`;
            const filePath = path.join(this.backupPath, fileName);
            
            await fs.writeFile(filePath, jsonData);

            // الحصول على حجم الملف الفعلي
            const stats = await fs.stat(filePath);
            const fileSize = stats.size;

            console.log(`✅ تم إنشاء النسخة الاحتياطية: ${fileName}`);
            console.log(`📦 حجم الملف: ${this.formatFileSize(fileSize)}`);

            // تنظيف النسخ القديمة
            await this.cleanOldBackups();

            return {
                success: true,
                fileName: fileName,
                filePath: filePath,
                size: fileSize,
                metadata: backupData.metadata
            };
            
        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // استعادة النسخة الاحتياطية
    async restoreBackup(filePath, overwriteExisting = false) {
        try {
            console.log('🔄 بدء استعادة النسخة الاحتياطية...');
            
            // قراءة الملف
            let fileData = await fs.readFile(filePath);
            console.log(`📁 تم قراءة الملف: ${path.basename(filePath)} (${fileData.length} بايت)`);

            // فك الضغط إذا كان مضغوطاً
            if (filePath.includes('.gz')) {
                console.log('📦 بدء فك ضغط النسخة الاحتياطية...');
                try {
                    fileData = await gunzip(fileData);
                    console.log('📦 تم فك ضغط النسخة الاحتياطية بنجاح');
                } catch (error) {
                    console.log('⚠️ فشل في فك الضغط، ربما البيانات غير مضغوطة:', error.message);
                    // إذا فشل فك الضغط، نفترض أن البيانات غير مضغوطة
                    console.log('📦 استخدام البيانات بدون فك ضغط');
                }
            }
            
            // تحليل البيانات
            const backupData = JSON.parse(fileData.toString());
            
            // التحقق من صحة النسخة الاحتياطية
            if (!this.validateBackupData(backupData)) {
                throw new Error('النسخة الاحتياطية غير صحيحة أو تالفة');
            }
            
            console.log(`📋 معلومات النسخة الاحتياطية:`);
            console.log(`   📅 التاريخ: ${backupData.metadata.timestamp}`);
            console.log(`   🔢 الإصدار: ${backupData.metadata.version}`);
            console.log(`   👥 المستخدمون: ${backupData.database.users?.length || 0}`);
            console.log(`   🤖 البوتات: ${backupData.database.bots?.length || 0}`);
            
            // نسخ احتياطية من البيانات الحالية قبل الاستعادة
            if (!overwriteExisting) {
                const currentBackup = await this.createBackup(false);
                if (currentBackup.success) {
                    console.log(`💾 تم إنشاء نسخة احتياطية من البيانات الحالية: ${currentBackup.fileName}`);
                }
            }
            
            // استعادة البيانات
            await this.restoreData(backupData.database);
            
            console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');
            
            return {
                success: true,
                metadata: backupData.metadata,
                restoredData: {
                    users: backupData.database.users?.length || 0,
                    bots: backupData.database.bots?.length || 0,
                    stats: backupData.database.botStats?.length || 0,
                    settings: backupData.database.settings?.length || 0
                }
            };
            
        } catch (error) {
            console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // تشفير البيانات
    async encryptData(data) {
        try {
            const password = config.database.backup.encryption.password;

            if (!password) {
                throw new Error('كلمة مرور التشفير غير محددة');
            }

            const key = crypto.scryptSync(password, 'backup_salt_2025', 32);
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

            let encrypted = cipher.update(data, 'utf8');
            encrypted = Buffer.concat([encrypted, cipher.final()]);

            // دمج IV + البيانات المشفرة
            const result = Buffer.concat([iv, encrypted]);
            console.log('🔒 تم تشفير البيانات بنجاح');
            return result;
        } catch (error) {
            console.error('خطأ في تشفير البيانات:', error);
            throw error;
        }
    }

    // فك تشفير البيانات
    async decryptData(encryptedData) {
        try {
            const password = config.database.backup.encryption.password;

            if (!password) {
                throw new Error('كلمة مرور فك التشفير غير محددة');
            }

            const key = crypto.scryptSync(password, 'backup_salt_2025', 32);
            const iv = encryptedData.slice(0, 16);
            const encrypted = encryptedData.slice(16);

            const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

            let decrypted = decipher.update(encrypted);
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            console.log('🔓 تم فك تشفير البيانات بنجاح');
            return decrypted.toString('utf8');
        } catch (error) {
            console.error('خطأ في فك تشفير البيانات:', error);
            throw error;
        }
    }

    // التحقق من صحة النسخة الاحتياطية
    validateBackupData(backupData) {
        if (!backupData || typeof backupData !== 'object') return false;
        if (!backupData.metadata || !backupData.database) return false;
        if (!backupData.metadata.timestamp || !backupData.metadata.version) return false;
        
        return true;
    }

    // استعادة البيانات إلى قاعدة البيانات
    async restoreData(databaseData) {
        // مسح البيانات الحالية (اختياري)
        // await this.db.clearAllData();
        
        // استعادة المستخدمين
        if (databaseData.users) {
            for (const user of databaseData.users) {
                await this.db.createUser(user.telegram_id, user.username);
                if (user.is_admin) {
                    await this.db.setAdmin(user.telegram_id, true);
                }
            }
        }
        
        // استعادة البوتات
        if (databaseData.bots) {
            for (const bot of databaseData.bots) {
                // التعامل مع تنسيقات مختلفة من النسخ الاحتياطية
                const serverHost = bot.server_host || bot.host || 'localhost';
                const serverPort = bot.server_port || bot.port || 25565;
                const botName = bot.bot_name || bot.name || 'UnknownBot';
                const minecraftVersion = bot.minecraft_version || bot.version || '1.20.1';
                const edition = bot.edition || 'java';
                const userId = bot.user_id || 1;

                console.log(`🔄 استعادة البوت: ${botName} (${serverHost}:${serverPort})`);

                try {
                    await this.db.createBot(
                        userId,
                        botName,
                        serverHost,
                        serverPort,
                        minecraftVersion,
                        edition
                    );
                    console.log(`✅ تم استعادة البوت: ${botName}`);
                } catch (error) {
                    console.error(`❌ خطأ في استعادة البوت ${botName}:`, error);
                }
            }
        }
        
        // استعادة الإعدادات
        if (databaseData.settings) {
            for (const setting of databaseData.settings) {
                await this.db.setSetting(setting.key, setting.value);
            }
        }
    }

    // الحصول على معلومات النظام
    async getSystemInfo() {
        const os = require('os');
        
        return {
            hostname: os.hostname(),
            platform: os.platform(),
            arch: os.arch(),
            nodeVersion: process.version,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpus: os.cpus().length,
            loadAverage: os.loadavg(),
            networkInterfaces: Object.keys(os.networkInterfaces()),
            timestamp: new Date().toISOString()
        };
    }

    // الحصول على إحصائيات النظام
    async getSystemStatistics() {
        try {
            // التحقق من وجود قاعدة البيانات
            if (!this.db) {
                console.log('⚠️ قاعدة البيانات غير متوفرة، استخدام قيم افتراضية');
                return {
                    totalUsers: 0,
                    totalBots: 0,
                    activeBots: 0,
                    totalStats: 0,
                    admins: 0,
                    timestamp: new Date().toISOString()
                };
            }

            const stats = {
                totalUsers: (await this.db.getAllUsers()).length,
                totalBots: (await this.db.getAllBots()).length,
                activeBots: (await this.db.getAllBots()).filter(bot => bot.status === 'running').length,
                totalStats: (await this.db.getAllBotStats()).length,
                admins: (await this.db.getAllAdmins()).length,
                timestamp: new Date().toISOString()
            };
            
            return stats;
        } catch (error) {
            console.error('خطأ في جمع الإحصائيات:', error);
            return null;
        }
    }

    // تنظيف النسخ القديمة (الاحتفاظ بـ 3 نسخ فقط)
    async cleanOldBackups() {
        try {
            const files = await fs.readdir(this.backupPath);
            const backupFiles = files
                .filter(file => file.startsWith('backup_') && (
                    file.endsWith('.json') ||
                    file.endsWith('.json.gz') ||
                    file.endsWith('.gz')
                ))
                .map(file => ({
                    name: file,
                    path: path.join(this.backupPath, file),
                    stat: null
                }));

            // الحصول على معلومات الملفات
            for (const file of backupFiles) {
                try {
                    file.stat = await fs.stat(file.path);
                } catch (error) {
                    console.error(`خطأ في قراءة معلومات الملف ${file.name}:`, error);
                }
            }

            // ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            backupFiles.sort((a, b) => {
                if (!a.stat || !b.stat) return 0;
                return b.stat.mtime - a.stat.mtime;
            });

            // الاحتفاظ بـ 3 نسخ فقط
            const maxBackups = 3;
            if (backupFiles.length > maxBackups) {
                const filesToDelete = backupFiles.slice(maxBackups);

                console.log(`📦 عدد النسخ الحالي: ${backupFiles.length}, سيتم حذف ${filesToDelete.length} نسخة قديمة`);

                for (const file of filesToDelete) {
                    try {
                        await fs.unlink(file.path);
                        console.log(`🗑️ تم حذف النسخة الاحتياطية القديمة: ${file.name}`);
                    } catch (error) {
                        console.error(`خطأ في حذف الملف ${file.name}:`, error);
                    }
                }

                console.log(`✅ تم الاحتفاظ بـ ${maxBackups} نسخ احتياطية فقط`);
            } else {
                console.log(`📦 عدد النسخ الحالي: ${backupFiles.length} (أقل من الحد الأقصى ${maxBackups})`);
            }

        } catch (error) {
            console.error('خطأ في تنظيف النسخ القديمة:', error);
        }
    }

    // بدء النسخ الاحتياطي التلقائي
    startAutoBackup(customInterval = null) {
        if (this.isAutoBackupEnabled) {
            console.log('⚠️ النسخ الاحتياطي التلقائي مفعل بالفعل');
            return false;
        }

        const interval = customInterval || config.database.backup.autoBackup.interval;

        // حفظ الفترة الزمنية الجديدة
        if (customInterval) {
            config.database.backup.autoBackup.interval = customInterval;
        }

        this.autoBackupInterval = setInterval(async () => {
            try {
                console.log('🔄 بدء النسخ الاحتياطي التلقائي...');

                const backup = await this.createBackup(true);

                if (backup.success && config.database.backup.autoBackup.sendToAdmin) {
                    await this.sendBackupToAdmins(backup);
                }

                // تنظيف النسخ التلقائية القديمة
                await this.cleanAutoBackups();

            } catch (error) {
                console.error('❌ خطأ في النسخ الاحتياطي التلقائي:', error);
            }
        }, interval);

        this.isAutoBackupEnabled = true;
        console.log(`✅ تم تفعيل النسخ الاحتياطي التلقائي (كل ${interval / 60000} دقيقة)`);
        return true;
    }

    // إيقاف النسخ الاحتياطي التلقائي
    stopAutoBackup() {
        if (!this.isAutoBackupEnabled) {
            console.log('⚠️ النسخ الاحتياطي التلقائي غير مفعل');
            return false;
        }

        if (this.autoBackupInterval) {
            clearInterval(this.autoBackupInterval);
            this.autoBackupInterval = null;
        }

        this.isAutoBackupEnabled = false;
        console.log('⏹️ تم إيقاف النسخ الاحتياطي التلقائي');
        return true;
    }

    // إرسال النسخة الاحتياطية للأدمن
    async sendBackupToAdmins(backup) {
        try {
            if (!this.telegramBot || !this.telegramBot.adminIds) {
                console.log('⚠️ لا يوجد أدمن لإرسال النسخة الاحتياطية إليهم');
                return;
            }

            const stats = await this.getSystemStatistics();
            const systemInfo = await this.getSystemInfo();

            // تنسيق معلومات النسخة الاحتياطية
            const backupInfo = this.formatBackupInfo(backup, stats, systemInfo);

            // إرسال النسخة الاحتياطية لكل أدمن
            for (const adminId of this.telegramBot.adminIds) {
                try {
                    // إرسال معلومات النسخة أولاً
                    await this.telegramBot.bot.sendMessage(adminId, backupInfo, {
                        parse_mode: 'Markdown'
                    });

                    // إرسال ملف النسخة الاحتياطية
                    await this.telegramBot.bot.sendDocument(adminId, backup.filePath, {
                        caption: `📦 نسخة احتياطية تلقائية\n📅 ${new Date().toLocaleString('ar-EG')}`,
                        parse_mode: 'Markdown'
                    });

                    console.log(`📤 تم إرسال النسخة الاحتياطية للأدمن: ${adminId}`);

                } catch (error) {
                    console.error(`❌ فشل في إرسال النسخة الاحتياطية للأدمن ${adminId}:`, error.message);
                }

                // تأخير بسيط لتجنب حدود التلغرام
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

        } catch (error) {
            console.error('❌ خطأ في إرسال النسخة الاحتياطية للأدمن:', error);
        }
    }

    // تنسيق معلومات النسخة الاحتياطية
    formatBackupInfo(backup, stats, systemInfo) {
        const size = this.formatFileSize(backup.size);
        const timestamp = new Date(backup.metadata.timestamp).toLocaleString('ar-EG');

        return `🔄 *نسخة احتياطية تلقائية*

📅 *التاريخ والوقت:* ${timestamp}
📦 *حجم الملف:* ${size}
🔢 *الإصدار:* ${backup.metadata.version}

📊 *إحصائيات النظام:*
👥 المستخدمون: ${stats?.totalUsers || 0}
🤖 البوتات: ${stats?.totalBots || 0}
🟢 البوتات النشطة: ${stats?.activeBots || 0}
👑 الأدمن: ${stats?.admins || 0}

💻 *معلومات الخادم:*
🖥️ المنصة: ${systemInfo?.platform || 'غير معروف'}
⚡ Node.js: ${systemInfo?.nodeVersion || 'غير معروف'}
🧠 الذاكرة: ${this.formatFileSize(systemInfo?.memory?.heapUsed || 0)}
⏱️ وقت التشغيل: ${this.formatUptime(systemInfo?.uptime || 0)}

ℹ️ *ملاحظة:* هذه نسخة احتياطية تلقائية. يمكنك استخدامها لاستعادة النظام في حالة حدوث مشكلة.`;
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';

        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // تنسيق وقت التشغيل
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) {
            return `${days} يوم، ${hours} ساعة، ${minutes} دقيقة`;
        } else if (hours > 0) {
            return `${hours} ساعة، ${minutes} دقيقة`;
        } else {
            return `${minutes} دقيقة`;
        }
    }

    // تنظيف النسخ التلقائية القديمة
    async cleanAutoBackups() {
        try {
            const files = await fs.readdir(this.backupPath);
            const autoBackupFiles = files
                .filter(file => file.startsWith('backup_') && file.includes('auto'))
                .map(file => ({
                    name: file,
                    path: path.join(this.backupPath, file),
                    stat: null
                }));

            // الحصول على معلومات الملفات
            for (const file of autoBackupFiles) {
                try {
                    file.stat = await fs.stat(file.path);
                } catch (error) {
                    console.error(`خطأ في قراءة معلومات الملف ${file.name}:`, error);
                }
            }

            // ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            autoBackupFiles.sort((a, b) => {
                if (!a.stat || !b.stat) return 0;
                return b.stat.mtime - a.stat.mtime;
            });

            // حذف الملفات الزائدة
            const maxAutoBackups = config.database.backup.autoBackup.maxAutoBackups;
            if (autoBackupFiles.length > maxAutoBackups) {
                const filesToDelete = autoBackupFiles.slice(maxAutoBackups);

                for (const file of filesToDelete) {
                    try {
                        await fs.unlink(file.path);
                        console.log(`🗑️ تم حذف النسخة التلقائية القديمة: ${file.name}`);
                    } catch (error) {
                        console.error(`خطأ في حذف الملف ${file.name}:`, error);
                    }
                }
            }

        } catch (error) {
            console.error('خطأ في تنظيف النسخ التلقائية القديمة:', error);
        }
    }

    // الحصول على حالة النسخ الاحتياطي
    getBackupStatus() {
        return {
            autoBackupEnabled: this.isAutoBackupEnabled,
            autoBackupInterval: config.database.backup.autoBackup.interval,
            interval: config.database.backup.autoBackup.interval,
            intervalMinutes: Math.floor(config.database.backup.autoBackup.interval / 60000),
            backupPath: this.backupPath,
            compressionEnabled: config.database.backup.compression.enabled,
            encryptionEnabled: config.database.backup.encryption.enabled,
            maxBackups: config.database.backup.maxBackups,
            maxAutoBackups: config.database.backup.autoBackup.maxAutoBackups
        };
    }

    // قائمة النسخ الاحتياطية المتوفرة
    async listBackups() {
        try {
            const files = await fs.readdir(this.backupPath);
            const backupFiles = [];

            for (const file of files) {
                if (file.startsWith('backup_') && (file.endsWith('.json') || file.includes('.json'))) {
                    try {
                        const filePath = path.join(this.backupPath, file);
                        const stat = await fs.stat(filePath);

                        backupFiles.push({
                            name: file,
                            path: filePath,
                            size: stat.size,
                            created: stat.birthtime,
                            modified: stat.mtime,
                            isAuto: file.includes('auto'),
                            isCompressed: file.includes('.gz'),
                            isEncrypted: file.includes('.enc')
                        });
                    } catch (error) {
                        console.error(`خطأ في قراءة معلومات الملف ${file}:`, error);
                    }
                }
            }

            // ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            backupFiles.sort((a, b) => b.created - a.created);

            return backupFiles;

        } catch (error) {
            console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error);
            return [];
        }
    }
}

module.exports = BackupManager;
