const http = require('http');
const { config } = require('./config');

class HealthChecker {
    constructor() {
        this.checks = new Map();
        this.lastResults = new Map();
        this.server = null;
    }

    // إضافة فحص صحة
    addCheck(name, checkFunction, interval = 30000) {
        this.checks.set(name, {
            function: checkFunction,
            interval: interval,
            lastRun: 0,
            lastResult: null,
            intervalId: null
        });
    }

    // بدء فحوصات الصحة
    start() {
        console.log('🏥 بدء فحوصات الصحة...');
        
        // إضافة الفحوصات الأساسية
        this.addBasicChecks();
        
        // بدء جميع الفحوصات
        for (const [name, check] of this.checks.entries()) {
            this.startCheck(name, check);
        }
        
        // بدء خادم الصحة
        this.startHealthServer();
    }

    // إضافة الفحوصات الأساسية
    addBasicChecks() {
        // فحص الذاكرة
        this.addCheck('memory', () => {
            const usage = process.memoryUsage();
            const maxMemory = config.bots.performance.maxMemoryUsage;
            const usagePercent = (usage.heapUsed / maxMemory) * 100;
            
            return {
                status: usagePercent < 90 ? 'healthy' : 'warning',
                details: {
                    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
                    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB',
                    usagePercent: Math.round(usagePercent) + '%'
                }
            };
        }, 10000);

        // فحص قاعدة البيانات
        this.addCheck('database', async () => {
            try {
                const Database = require('./database');
                const db = new Database();
                await db.init();
                
                return {
                    status: 'healthy',
                    details: { message: 'قاعدة البيانات متصلة' }
                };
            } catch (error) {
                return {
                    status: 'unhealthy',
                    details: { error: error.message }
                };
            }
        }, 30000);

        // فحص التلغرام
        this.addCheck('telegram', () => {
            const token = config.telegram.token;
            if (!token || token === 'YOUR_BOT_TOKEN_HERE') {
                return {
                    status: 'unhealthy',
                    details: { error: 'توكن التلغرام غير صحيح' }
                };
            }
            
            return {
                status: 'healthy',
                details: { message: 'توكن التلغرام صحيح' }
            };
        }, 60000);

        // فحص الشبكة
        this.addCheck('network', async () => {
            try {
                const dns = require('dns').promises;
                await dns.lookup('google.com');
                
                return {
                    status: 'healthy',
                    details: { message: 'الاتصال بالإنترنت متاح' }
                };
            } catch (error) {
                return {
                    status: 'unhealthy',
                    details: { error: 'لا يوجد اتصال بالإنترنت' }
                };
            }
        }, 30000);
    }

    // بدء فحص معين
    startCheck(name, check) {
        const runCheck = async () => {
            try {
                const result = await check.function();
                check.lastResult = {
                    ...result,
                    timestamp: new Date().toISOString(),
                    name: name
                };
                check.lastRun = Date.now();
                
                this.lastResults.set(name, check.lastResult);
                
                if (result.status !== 'healthy') {
                    console.log(`⚠️ فحص الصحة ${name}: ${result.status}`, result.details);
                }
            } catch (error) {
                const errorResult = {
                    status: 'error',
                    details: { error: error.message },
                    timestamp: new Date().toISOString(),
                    name: name
                };
                
                check.lastResult = errorResult;
                this.lastResults.set(name, errorResult);
                
                console.error(`❌ خطأ في فحص الصحة ${name}:`, error.message);
            }
        };

        // تشغيل الفحص فوراً
        runCheck();
        
        // جدولة الفحص المتكرر
        check.intervalId = setInterval(runCheck, check.interval);
    }

    // إيقاف فحص معين
    stopCheck(name) {
        const check = this.checks.get(name);
        if (check && check.intervalId) {
            clearInterval(check.intervalId);
            check.intervalId = null;
        }
    }

    // إيقاف جميع الفحوصات
    stop() {
        console.log('🛑 إيقاف فحوصات الصحة...');
        
        for (const name of this.checks.keys()) {
            this.stopCheck(name);
        }
        
        if (this.server) {
            this.server.close();
            this.server = null;
        }
    }

    // بدء خادم الصحة
    startHealthServer() {
        const port = process.env.HEALTH_CHECK_PORT || process.env.PORT || 8080;
        
        this.server = http.createServer((req, res) => {
            // إضافة CORS headers
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            if (req.url === '/health') {
                this.handleHealthRequest(req, res);
            } else if (req.url === '/health/detailed') {
                this.handleDetailedHealthRequest(req, res);
            } else if (req.url === '/') {
                // صفحة رئيسية بسيطة
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html dir="rtl" lang="ar">
                    <head>
                        <meta charset="UTF-8">
                        <title>نظام بوت ماينكرافت</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
                            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
                            h1 { color: #2c3e50; }
                            .status { color: #27ae60; font-size: 18px; margin: 20px 0; }
                            .links { margin-top: 30px; }
                            .links a { display: inline-block; margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🤖 نظام بوت ماينكرافت</h1>
                            <div class="status">✅ النظام يعمل بنجاح</div>
                            <p>نظام متكامل لإنشاء وإدارة بوتات ماينكرافت Java و Bedrock عبر بوت التلغرام</p>
                            <div class="links">
                                <a href="/health">فحص الصحة</a>
                                <a href="/health/detailed">تقرير مفصل</a>
                            </div>
                        </div>
                    </body>
                    </html>
                `);
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Not found' }));
            }
        });

        this.server.listen(port, () => {
            console.log(`🏥 خادم فحص الصحة يعمل على المنفذ ${port}`);
        });
    }

    // معالجة طلب الصحة البسيط
    handleHealthRequest(req, res) {
        try {
            const overallStatus = this.getOverallStatus();
            // دائماً إرجاع 200 للـ healthcheck (Railway يحتاج 200)
            const statusCode = 200;

            res.writeHead(statusCode, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                status: overallStatus,
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                message: 'Health check endpoint is working'
            }));
        } catch (error) {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                status: 'error',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                error: error.message,
                message: 'Health check endpoint is working but with errors'
            }));
        }
    }

    // معالجة طلب الصحة المفصل
    handleDetailedHealthRequest(req, res) {
        const results = {};
        
        for (const [name, result] of this.lastResults.entries()) {
            results[name] = result;
        }
        
        const overallStatus = this.getOverallStatus();
        const statusCode = overallStatus === 'healthy' ? 200 : 503;
        
        res.writeHead(statusCode, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            checks: results,
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memory: process.memoryUsage(),
                cpu: process.cpuUsage()
            }
        }));
    }

    // الحصول على الحالة العامة
    getOverallStatus() {
        if (this.lastResults.size === 0) {
            return 'unknown';
        }
        
        let hasUnhealthy = false;
        let hasWarning = false;
        
        for (const result of this.lastResults.values()) {
            if (result.status === 'unhealthy' || result.status === 'error') {
                hasUnhealthy = true;
                break;
            } else if (result.status === 'warning') {
                hasWarning = true;
            }
        }
        
        if (hasUnhealthy) return 'unhealthy';
        if (hasWarning) return 'warning';
        return 'healthy';
    }

    // الحصول على تقرير الصحة
    getHealthReport() {
        return {
            status: this.getOverallStatus(),
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            checks: Object.fromEntries(this.lastResults),
            summary: {
                total: this.lastResults.size,
                healthy: Array.from(this.lastResults.values()).filter(r => r.status === 'healthy').length,
                warning: Array.from(this.lastResults.values()).filter(r => r.status === 'warning').length,
                unhealthy: Array.from(this.lastResults.values()).filter(r => r.status === 'unhealthy' || r.status === 'error').length
            }
        };
    }
}

// إنشاء مثيل واحد للاستخدام العام
const healthChecker = new HealthChecker();

module.exports = healthChecker;
