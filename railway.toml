[build]
builder = "nixpacks"

[deploy]
startCommand = "npm start"
healthcheckPath = "/"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[environments.production]
variables = { NODE_ENV = "production" }

[environments.development]
variables = { NODE_ENV = "development", DEBUG_MODE = "true" }

# إعدادات الشبكة
[networking]
serviceDomains = []

# إعدادات الموارد
[resources]
# تحديد الحد الأدنى والأقصى للذاكرة
memoryLimit = "512Mi"
cpuLimit = "500m"

# إعدادات التخزين المؤقت
[volumes]
# يمكن إضافة volumes للتخزين المستمر إذا لزم الأمر

# إعدادات النسخ الاحتياطي
[backup]
# يمكن تفعيل النسخ الاحتياطي التلقائي

# إعدادات المراقبة
[monitoring]
# تفعيل مراقبة الأداء والصحة
healthcheck = true
metrics = true

# إعدادات الأمان
[security]
# تفعيل HTTPS
forceHttps = true

# إعدادات البيئة المطلوبة
[env]
# متغيرات البيئة الأساسية - يجب تعيينها في لوحة التحكم
TELEGRAM_BOT_TOKEN = { required = true, description = "توكن بوت التلغرام" }
ADMIN_IDS = { required = false, description = "معرفات الأدمن مفصولة بفواصل" }
DATABASE_URL = { required = false, description = "رابط قاعدة البيانات (اختياري للـ PostgreSQL)" }
NODE_ENV = { default = "production" }
PORT = { default = "3000" }
LOG_LEVEL = { default = "info" }
MAX_BOTS_PER_USER = { default = "3" }
RATE_LIMIT_WINDOW = { default = "60000" }
MAX_REQUESTS_PER_WINDOW = { default = "30" }
CONNECTION_TIMEOUT = { default = "30000" }
KEEP_ALIVE_TIMEOUT = { default = "30000" }
RESPONSE_TIMEOUT = { default = "10000" }
MAX_RECONNECT_ATTEMPTS = { default = "5" }
RECONNECT_DELAY = { default = "5000" }
BACKOFF_MULTIPLIER = { default = "1.5" }
MAX_MEMORY_USAGE = { default = "536870912" }
GC_INTERVAL = { default = "300000" }
STATS_UPDATE_INTERVAL = { default = "60000" }
MONITORING_ENABLED = { default = "true" }
MONITORING_INTERVAL = { default = "60000" }
PERFORMANCE_MONITORING = { default = "true" }
LOG_TO_FILE = { default = "false" }
ALERTS_ENABLED = { default = "true" }
MEMORY_THRESHOLD = { default = "80" }
ERROR_RATE_THRESHOLD = { default = "10" }
CONNECTION_FAILURE_THRESHOLD = { default = "5" }
BACKUP_ENABLED = { default = "false" }
AUTO_BACKUP_ENABLED = { default = "false" }
AUTO_BACKUP_INTERVAL = { default = "300000" }
AUTO_BACKUP_SEND_TO_ADMIN = { default = "true" }
AUTO_BACKUP_KEEP_IN_CHAT = { default = "true" }
MAX_AUTO_BACKUPS = { default = "20" }
BACKUP_COMPRESSION = { default = "true" }
BACKUP_COMPRESSION_LEVEL = { default = "6" }
BACKUP_ENCRYPTION = { default = "false" }
BACKUP_PASSWORD = { required = false, description = "كلمة مرور تشفير النسخ الاحتياطي (اختياري)" }
ENABLE_GZIP = { default = "true" }
MINIFY_RESPONSES = { default = "true" }
CACHE_STATIC = { default = "true" }
CLUSTER_ENABLED = { default = "false" }
