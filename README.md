# 🎮 نظام بوت ماينكرافت المتقدم

نظام متكامل لإنشاء وإدارة بوتات ماينكرافت Java و Bedrock عبر بوت التلغرام مع دعم كامل للنشر على Railway.com

## ✨ الميزات

- 🤖 **إنشاء بوتات ماينكرافت**: دعم كامل لـ Java و Bedrock
- 📱 **واجهة تلغرام سهلة**: تحكم كامل عبر بوت التلغرام
- 🔄 **إعادة الاتصال التلقائي**: مراقبة السيرفرات وإعادة الاتصال عند الانقطاع
- 📊 **إحصائيات مفصلة**: مراقبة الأداء والاستخدام
- 🔒 **أمان متقدم**: حماية من الهجمات وتشفير البيانات
- 🌐 **دعم السحابة**: جاهز للنشر على Railway.com
- 💾 **قواعد بيانات متعددة**: دعم SQLite و PostgreSQL و MySQL و MariaDB و SQL Server و Oracle و MongoDB و Redis
- 🔧 **إعدادات مرنة**: تخصيص كامل عبر متغيرات البيئة
- 💾 **نظام النسخ الاحتياطي**: نسخ احتياطي تلقائي ويدوي مع إمكانية الاستعادة الكاملة

## 🚀 النشر على Railway.com

### 1. إعداد المستودع

```bash
# استنساخ المشروع
git clone https://github.com/your-username/minecraft-telegram-bot-system.git
cd minecraft-telegram-bot-system

# تثبيت التبعيات
npm install
```

### 2. إعداد متغيرات البيئة

انسخ ملف `.env.example` إلى `.env` وقم بتعديل القيم:

```bash
cp .env.example .env
```

**المتغيرات المطلوبة:**
- `TELEGRAM_BOT_TOKEN`: توكن بوت التلغرام
- `ADMIN_IDS`: معرفات الأدمن (مفصولة بفواصل)

### 3. النشر على Railway

1. **إنشاء حساب على Railway.com**
2. **ربط مستودع GitHub**
3. **تعيين متغيرات البيئة في لوحة التحكم**
4. **النشر التلقائي**

## 🔧 التثبيت المحلي

### المتطلبات

- Node.js 18+ 
- npm 8+
- Git

### خطوات التثبيت

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/minecraft-telegram-bot-system.git
cd minecraft-telegram-bot-system

# 2. تثبيت التبعيات
npm install

# 3. إعداد متغيرات البيئة
cp .env.example .env
# قم بتعديل ملف .env بالقيم المناسبة

# 4. تشغيل المشروع
npm start

# للتطوير
npm run dev
```

## 📋 متغيرات البيئة

### الأساسية
- `TELEGRAM_BOT_TOKEN`: توكن بوت التلغرام (مطلوب)
- `ADMIN_IDS`: معرفات الأدمن مفصولة بفواصل
- `NODE_ENV`: بيئة التشغيل (production/development)
- `PORT`: منفذ الخدمة (افتراضي: 3000)

### قاعدة البيانات
- `DATABASE_URL`: رابط قاعدة البيانات
  - SQLite: `sqlite:./minecraft_bot.db`
  - PostgreSQL: `postgresql://user:pass@host:port/db`
  - MySQL: `mysql://user:pass@host:port/db`
  - MariaDB: `mariadb://user:pass@host:port/db`
  - SQL Server: `mssql://user:pass@host:port/db`
  - Oracle: `oracle://user:pass@host:port/db`
  - MongoDB: `************************:port/db`
  - Redis: `redis://user:pass@host:port`

### الأمان
- `RATE_LIMIT_WINDOW`: نافذة معدل الطلبات (60000)
- `MAX_REQUESTS_PER_WINDOW`: حد الطلبات (30)
- `MAX_BOTS_PER_USER`: حد البوتات لكل مستخدم (3)

### الأداء
- `MAX_MEMORY_USAGE`: حد الذاكرة بالبايت
- `CONNECTION_TIMEOUT`: مهلة الاتصال
- `MONITORING_ENABLED`: تفعيل المراقبة

### النسخ الاحتياطي
- `AUTO_BACKUP_ENABLED`: تفعيل النسخ التلقائي (false)
- `AUTO_BACKUP_INTERVAL`: فترة النسخ التلقائي بالميلي ثانية (300000 = 5 دقائق)
- `AUTO_BACKUP_SEND_TO_ADMIN`: إرسال النسخ للأدمن (true)
- `BACKUP_COMPRESSION`: تفعيل ضغط النسخ (true)
- `BACKUP_ENCRYPTION`: تفعيل تشفير النسخ (false)
- `BACKUP_PASSWORD`: كلمة مرور التشفير (اختياري)

## 🎯 الاستخدام

### إنشاء بوت جديد

1. ابدأ محادثة مع بوت التلغرام
2. استخدم الأمر `/start`
3. اختر "إنشاء بوت جديد"
4. اتبع التعليمات لإدخال:
   - اسم البوت
   - عنوان السيرفر
   - منفذ السيرفر
   - إصدار ماينكرافت
   - نوع الإصدار (Java/Bedrock)

### إدارة البوتات

- `/mybots` - عرض بوتاتك
- `/start_bot` - تشغيل بوت
- `/stop_bot` - إيقاف بوت
- `/bot_status` - حالة البوت
- `/send_message` - إرسال رسالة
- `/send_command` - تنفيذ أمر

### أوامر الأدمن

- `/admin` - لوحة تحكم الأدمن
- `/stats` - إحصائيات النظام
- `/users` - إدارة المستخدمين
- `/system` - معلومات النظام
- `/backup` - إدارة النسخ الاحتياطي
- `/restore` - استعادة نسخة احتياطية

## 💾 نظام النسخ الاحتياطي

### الميزات الأساسية
- **نسخ احتياطي يدوي**: إنشاء نسخ احتياطية عند الطلب
- **نسخ احتياطي تلقائي**: نسخ تلقائية كل 5 دقائق (قابل للتخصيص)
- **استعادة كاملة**: استعادة جميع البيانات من ملف النسخة الاحتياطية
- **ضغط وتشفير**: ضغط الملفات وتشفيرها لحماية إضافية
- **إرسال تلقائي**: إرسال النسخ التلقائية للأدمن عبر التلغرام

### كيفية الاستخدام
1. **الوصول للوحة النسخ الاحتياطي**: `/admin` ← `💾 النسخ الاحتياطي`
2. **إنشاء نسخة احتياطية**: انقر على `📦 إنشاء نسخة احتياطية`
3. **تفعيل النسخ التلقائي**: انقر على `▶️ تشغيل النسخ التلقائي`
4. **استعادة نسخة**: انقر على `🔄 استعادة نسخة احتياطية` وأرسل الملف

### معلومات النسخة الاحتياطية
تحتوي كل نسخة احتياطية على:
- جميع بيانات المستخدمين والبوتات
- إحصائيات الاستخدام والأداء
- إعدادات النظام والأدمن
- معلومات النظام والخادم

## 🔒 الأمان

- **حماية معدل الطلبات**: منع الإفراط في الاستخدام
- **تشفير البيانات**: حماية المعلومات الحساسة
- **مصادقة الجلسات**: رموز أمان للجلسات
- **مراقبة الأنشطة**: تسجيل العمليات المشبوهة
- **نسخ احتياطي آمنة**: تشفير النسخ الاحتياطية (اختياري)

## 📊 المراقبة

- **مراقبة الأداء**: استخدام الذاكرة والمعالج
- **مراقبة الاتصالات**: حالة اتصال البوتات
- **إحصائيات الاستخدام**: عدد الرسائل والأوامر
- **تنبيهات تلقائية**: إشعارات عند المشاكل

## 🐳 Docker

```bash
# بناء الصورة
docker build -t minecraft-bot-system .

# تشغيل الحاوية
docker run -d \
  --name minecraft-bot \
  -e TELEGRAM_BOT_TOKEN=your_token \
  -e ADMIN_IDS=your_admin_ids \
  -p 3000:3000 \
  minecraft-bot-system
```

## 🔧 التطوير

### هيكل المشروع

```
├── index.js              # نقطة البداية
├── config.js             # إعدادات النظام
├── telegram-bot.js       # بوت التلغرام
├── bot-manager.js        # إدارة البوتات
├── database.js           # قاعدة البيانات
├── security.js           # إدارة الأمان
├── minecraft-java-bot.js # بوت Java
├── minecraft-bedrock-bot.js # بوت Bedrock
├── railway.toml          # إعدادات Railway
├── Dockerfile            # إعدادات Docker
└── .env.example          # مثال متغيرات البيئة
```

### إضافة ميزات جديدة

1. إنشاء فرع جديد
2. تطوير الميزة
3. اختبار شامل
4. إنشاء Pull Request

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بالتلغرام:**
- تحقق من صحة التوكن
- تأكد من الاتصال بالإنترنت

**فشل اتصال البوت بالسيرفر:**
- تحقق من عنوان السيرفر والمنفذ
- تأكد من أن السيرفر يقبل الاتصالات

**مشاكل قاعدة البيانات:**
- تحقق من صحة DATABASE_URL
- تأكد من الصلاحيات

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. إنشاء Pull Request

## 📞 الدعم

- **GitHub Issues**: للإبلاغ عن الأخطاء
- **Discussions**: للأسئلة والاقتراحات
- **Email**: <EMAIL>

## 🙏 شكر خاص

- [Mineflayer](https://github.com/PrismarineJS/mineflayer) - مكتبة بوت Java
- [Bedrock Protocol](https://github.com/PrismarineJS/bedrock-protocol) - مكتبة بوت Bedrock
- [Node Telegram Bot API](https://github.com/yagop/node-telegram-bot-api) - مكتبة التلغرام

---

<div align="center">
  <strong>صُنع بـ ❤️ للمجتمع العربي</strong>
</div>
