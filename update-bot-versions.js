const Database = require('./database');

async function updateBotVersions() {
    console.log('🔄 بدء تحديث إصدارات البوتات...');
    
    const db = await new Database().init();
    
    // الحصول على جميع البوتات
    const bots = await db.getAllBots();
    
    // الإصدارات القديمة التي نريد تحديثها
    const oldVersions = ['1.21.50', '1.21.44', '1.21.40', '1.21.30'];
    const newVersion = '1.21.93'; // الإصدار الجديد المدعوم
    
    let updatedCount = 0;
    
    for (const bot of bots) {
        if (bot.edition === 'bedrock' && oldVersions.includes(bot.minecraft_version)) {
            console.log(`🔄 تحديث البوت ${bot.bot_name} من ${bot.minecraft_version} إلى ${newVersion}`);
            
            // تحديث الإصدار في قاعدة البيانات
            const result = await db.updateBotVersion(bot.id, newVersion);
            if (!result) {
                console.log(`❌ فشل في تحديث البوت ${bot.bot_name}`);
                continue;
            }
            
            updatedCount++;
        }
    }
    
    console.log(`✅ تم تحديث ${updatedCount} بوت بنجاح`);
    console.log('🎯 يمكنك الآن تشغيل البوتات بدون مشاكل');
    
    process.exit(0);
}

updateBotVersions().catch(console.error);
