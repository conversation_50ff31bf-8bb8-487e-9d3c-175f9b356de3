const Database = require('./database');

async function fixDatabase() {
    console.log('🔧 إصلاح قاعدة البيانات...');
    
    const db = await new Database().init();
    
    try {
        // إنشاء جدول جديد بالحالات الصحيحة
        await new Promise((resolve, reject) => {
            db.db.run(`
                CREATE TABLE IF NOT EXISTS bots_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    bot_name TEXT NOT NULL,
                    server_host TEXT NOT NULL,
                    server_port INTEGER NOT NULL,
                    minecraft_version TEXT NOT NULL,
                    edition TEXT NOT NULL CHECK(edition IN ('java', 'bedrock')),
                    status TEXT DEFAULT 'stopped' CHECK(status IN ('running', 'stopped', 'connecting', 'error')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        // نسخ البيانات من الجدول القديم
        await new Promise((resolve, reject) => {
            db.db.run(`
                INSERT INTO bots_new (id, user_id, bot_name, server_host, server_port, minecraft_version, edition, status, created_at, updated_at)
                SELECT id, user_id, bot_name, server_host, server_port, minecraft_version, edition, status, created_at, updated_at
                FROM bots
            `, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        // حذف الجدول القديم
        await new Promise((resolve, reject) => {
            db.db.run('DROP TABLE bots', (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        // إعادة تسمية الجدول الجديد
        await new Promise((resolve, reject) => {
            db.db.run('ALTER TABLE bots_new RENAME TO bots', (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        console.log('✅ تم إصلاح قاعدة البيانات بنجاح');
        console.log('🎯 يمكنك الآن تشغيل البوت بدون مشاكل');
        
    } catch (error) {
        console.error('❌ خطأ في إصلاح قاعدة البيانات:', error);
    }
    
    process.exit(0);
}

fixDatabase().catch(console.error);
