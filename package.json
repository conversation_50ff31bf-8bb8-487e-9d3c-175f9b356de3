{"name": "minecraft-telegram-bot-system", "version": "1.0.0", "description": "نظام متكامل لإنشاء وإدارة بوتات ماينكرافت Java و Bedrock عبر بوت التلغرام", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step required'", "test": "node test/validate-setup.js", "test:health": "curl -f http://localhost:3001/health || exit 1", "test:config": "node -e \"require('./config'); console.log('Config loaded successfully')\"", "health-check": "node health-check.js", "test:backup": "node test-backup.js", "test:complete": "node test-complete-system.js", "test:all": "npm run validate && npm run test:backup && npm run test:complete", "postinstall": "echo 'Installation complete'", "lint": "echo '<PERSON><PERSON> not configured'", "validate": "node test/validate-setup.js"}, "keywords": ["minecraft", "bot", "telegram", "java", "bedrock", "mineflayer", "bedrock-protocol", "aternos"], "author": "Minecraft Bot System Developer", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/minecraft-telegram-bot-system.git"}, "dependencies": {"bedrock-protocol": "^3.47.0", "dotenv": "^16.6.1", "mineflayer": "^4.20.0", "node-telegram-bot-api": "^0.66.0", "node-fetch": "^2.7.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "mysql2": "^3.14.2", "pg": "^8.11.3", "mariadb": "^3.2.2"}, "optionalDependencies": {"mongodb": "^6.3.0", "redis": "^4.6.13", "tedious": "^18.6.1", "oracledb": "^6.6.0"}}