const Database = require('./database');
const fs = require('fs').promises;
const path = require('path');

/**
 * تطبيق التحسينات على النظام الموجود
 * يحدث قاعدة البيانات والإعدادات لتتوافق مع النظام المحسن
 */
class EnhancementApplicator {
    constructor() {
        this.db = null;
        this.changes = [];
    }

    async init() {
        console.log('🔧 تهيئة تطبيق التحسينات...');
        this.db = await new Database().init();
        console.log('✅ تم تهيئة قاعدة البيانات');
    }

    // تحديث إصدارات البوتات الموجودة
    async updateBotVersions() {
        console.log('\n📋 تحديث إصدارات البوتات الموجودة...');
        
        try {
            const bots = await this.db.getAllBots();
            let updatedCount = 0;
            
            for (const bot of bots) {
                let needsUpdate = false;
                let newVersion = bot.minecraft_version;
                
                if (bot.edition === 'java') {
                    // الإصدارات الجديدة المدعومة لـ Java
                    const supportedJavaVersions = ['1.21.4', '1.21.3', '1.21.1', '1.21.0', '1.20.6'];
                    
                    if (!supportedJavaVersions.includes(bot.minecraft_version)) {
                        // إذا كان الإصدار غير مدعوم، استخدم أحدث إصدار
                        newVersion = '1.21.4';
                        needsUpdate = true;
                    }
                } else if (bot.edition === 'bedrock') {
                    // الإصدارات الجديدة المدعومة لـ Bedrock
                    const supportedBedrockVersions = ['1.21.93', '1.21.92', '1.21.90', '1.21.80', '1.20.30'];

                    if (!supportedBedrockVersions.includes(bot.minecraft_version)) {
                        // إذا كان الإصدار غير مدعوم، استخدم أحدث إصدار
                        newVersion = '1.21.93';
                        needsUpdate = true;
                    }
                }
                
                if (needsUpdate) {
                    console.log(`🔄 تحديث البوت ${bot.bot_name} من ${bot.minecraft_version} إلى ${newVersion}`);
                    
                    const result = await this.db.updateBotVersion(bot.id, newVersion);
                    if (result) {
                        updatedCount++;
                        this.changes.push(`تحديث البوت ${bot.bot_name} إلى الإصدار ${newVersion}`);
                    }
                }
            }
            
            console.log(`✅ تم تحديث ${updatedCount} بوت بنجاح`);
            return updatedCount;
            
        } catch (error) {
            console.error('❌ خطأ في تحديث إصدارات البوتات:', error);
            return 0;
        }
    }

    // إنشاء ملف إعدادات البيئة المحسن
    async createEnhancedEnvFile() {
        console.log('\n⚙️ إنشاء ملف إعدادات البيئة المحسن...');
        
        const enhancedEnvContent = `# إعدادات النظام المحسن
# Enhanced System Configuration

# إعدادات بوت التلغرام
TELEGRAM_BOT_TOKEN=YOUR_BOT_TOKEN_HERE
ADMIN_IDS=123456789,987654321

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:./minecraft_bot.db

# إعدادات إعادة الاتصال المحسنة
MAX_RECONNECT_ATTEMPTS=15
RECONNECT_DELAY=20000
BACKOFF_MULTIPLIER=1.0

# إعدادات المراقبة
MONITORING_INTERVAL=20000

# إعدادات الأمان
RATE_LIMIT_WINDOW=60000
MAX_REQUESTS_PER_WINDOW=30
MAX_BOTS_PER_USER=3

# إعدادات الأداء
CONNECTION_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=30000
RESPONSE_TIMEOUT=10000

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
AUTO_BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
MAX_BACKUPS=7

# إعدادات التطوير
DEBUG_MODE=false
NODE_ENV=production
LOG_LEVEL=info

# إعدادات الشبكة
PORT=3000
`;

        try {
            await fs.writeFile('.env.enhanced', enhancedEnvContent);
            console.log('✅ تم إنشاء ملف .env.enhanced بنجاح');
            this.changes.push('إنشاء ملف إعدادات البيئة المحسن (.env.enhanced)');
            return true;
        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف الإعدادات:', error);
            return false;
        }
    }

    // إنشاء ملف تشغيل سريع
    async createQuickStartScript() {
        console.log('\n🚀 إنشاء ملف التشغيل السريع...');
        
        const quickStartContent = `#!/bin/bash
# ملف التشغيل السريع للنظام المحسن
# Enhanced System Quick Start Script

echo "🚀 بدء تشغيل نظام بوت ماينكرافت المحسن..."
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً."
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت. يرجى تثبيت npm أولاً."
    exit 1
fi

echo "✅ Node.js و npm متوفران"

# تثبيت التبعيات إذا لم تكن موجودة
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت التبعيات..."
    npm install
fi

# التحقق من ملف الإعدادات
if [ ! -f ".env" ]; then
    if [ -f ".env.enhanced" ]; then
        echo "📋 نسخ إعدادات النظام المحسن..."
        cp .env.enhanced .env
    else
        echo "⚠️ ملف الإعدادات غير موجود. يرجى إنشاء ملف .env"
        echo "💡 يمكنك استخدام .env.enhanced كنموذج"
        exit 1
    fi
fi

echo "🔧 التحقق من صحة الإعدادات..."
node -e "
try {
    const {config, validateConfig} = require('./config');
    const validation = validateConfig();
    if (validation.valid) {
        console.log('✅ جميع الإعدادات صحيحة');
    } else {
        console.log('❌ مشاكل في الإعدادات:');
        validation.errors.forEach(error => console.log('  -', error));
        process.exit(1);
    }
} catch (error) {
    console.log('❌ خطأ في تحميل الإعدادات:', error.message);
    process.exit(1);
}
"

if [ $? -ne 0 ]; then
    echo "❌ فشل في التحقق من الإعدادات"
    exit 1
fi

echo ""
echo "🎯 الميزات المحسنة:"
echo "  • دعم آخر 5 إصدارات Java وآخر 4 إصدارات Bedrock + 1.20.30"
echo "  • إعادة اتصال كل 20 ثانية لمدة 5 دقائق"
echo "  • إشعارات محسنة ومفصلة"
echo "  • إشعار فوري عند دخول العالم"
echo "  • نظام مراقبة محسن"
echo ""

echo "🚀 بدء تشغيل النظام..."
npm start
`;

        try {
            await fs.writeFile('quick-start.sh', quickStartContent);
            console.log('✅ تم إنشاء ملف quick-start.sh بنجاح');
            this.changes.push('إنشاء ملف التشغيل السريع (quick-start.sh)');
            return true;
        } catch (error) {
            console.error('❌ خطأ في إنشاء ملف التشغيل السريع:', error);
            return false;
        }
    }

    // عرض ملخص التحسينات المطبقة
    displaySummary() {
        console.log('\n📊 ملخص التحسينات المطبقة:');
        console.log('=' .repeat(50));
        
        if (this.changes.length === 0) {
            console.log('ℹ️ لا توجد تغييرات مطلوبة - النظام محدث بالفعل');
        } else {
            this.changes.forEach((change, index) => {
                console.log(`${index + 1}. ${change}`);
            });
        }
        
        console.log('=' .repeat(50));
        console.log('\n🎯 الميزات المحسنة:');
        console.log('✅ الإصدارات المدعومة محدثة');
        console.log('✅ نظام إعادة الاتصال محسن (كل 20 ثانية)');
        console.log('✅ نظام الإشعارات مطور');
        console.log('✅ نظام المراقبة محسن');
        console.log('✅ إشعار فوري عند دخول العالم');
        
        console.log('\n🚀 للتشغيل:');
        console.log('   npm start');
        console.log('   أو');
        console.log('   bash quick-start.sh');
    }

    // تطبيق جميع التحسينات
    async applyAllEnhancements() {
        try {
            await this.init();
            
            console.log('🔧 تطبيق التحسينات على النظام...');
            console.log('📋 التحسينات المطبقة:');
            console.log('   • تحديث إصدارات البوتات');
            console.log('   • إنشاء ملف إعدادات محسن');
            console.log('   • إنشاء ملف تشغيل سريع');
            
            const updatedBots = await this.updateBotVersions();
            const envCreated = await this.createEnhancedEnvFile();
            const scriptCreated = await this.createQuickStartScript();
            
            this.displaySummary();
            
            console.log('\n🎉 تم تطبيق جميع التحسينات بنجاح!');
            console.log('💡 يمكنك الآن تشغيل النظام المحسن');
            
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في تطبيق التحسينات:', error);
            return false;
        }
    }
}

// تشغيل التطبيق إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const applicator = new EnhancementApplicator();
    applicator.applyAllEnhancements().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ فشل في تطبيق التحسينات:', error);
        process.exit(1);
    });
}

module.exports = EnhancementApplicator;
