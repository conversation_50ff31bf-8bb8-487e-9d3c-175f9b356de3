const Database = require('./database');
const BotManager = require('./bot-manager');
const { config } = require('./config');

/**
 * اختبار نظام التحذيرات المحدث
 * إعادة الاتصال: كل 20 ثانية لمدة 5 دقائق (15 محاولة)
 * التحذيرات: أول رسالة فوراً، ثم كل دقيقة (5 رسائل إجمالية)
 */
class UpdatedWarningSystemTest {
    constructor() {
        this.db = null;
        this.botManager = null;
        this.testResults = [];
    }

    async init() {
        console.log('🔧 تهيئة اختبار نظام التحذيرات المحدث...');
        this.db = await new Database().init();
        this.botManager = await new BotManager().init();
        console.log('✅ تم تهيئة النظام للاختبار');
    }

    // اختبار إعدادات إعادة الاتصال
    testReconnectionSettings() {
        console.log('\n🔄 اختبار إعدادات إعادة الاتصال...');
        
        const expectedMaxAttempts = 15; // 15 محاولة (كل 20 ثانية لمدة 5 دقائق)
        const expectedDelay = 20000; // 20 ثانية
        
        const actualMaxAttempts = config.bots.reconnection.maxAttempts;
        const actualDelay = config.bots.reconnection.delay;
        
        const maxAttemptsMatch = expectedMaxAttempts === actualMaxAttempts;
        const delayMatch = expectedDelay === actualDelay;
        
        console.log(`   ${maxAttemptsMatch ? '✅' : '❌'} محاولات إعادة الاتصال: ${actualMaxAttempts} (المطلوب: ${expectedMaxAttempts})`);
        console.log(`   ${delayMatch ? '✅' : '❌'} فترة إعادة الاتصال: ${actualDelay}ms (المطلوب: ${expectedDelay}ms)`);
        
        // حساب المدة الإجمالية
        const totalDuration = (actualMaxAttempts * actualDelay) / 1000 / 60; // بالدقائق
        const expectedDuration = 5; // 5 دقائق
        const durationMatch = Math.abs(totalDuration - expectedDuration) < 0.1;
        
        console.log(`   ${durationMatch ? '✅' : '❌'} المدة الإجمالية: ${totalDuration.toFixed(1)} دقيقة (المطلوب: ${expectedDuration} دقائق)`);
        
        const allMatch = maxAttemptsMatch && delayMatch && durationMatch;
        
        this.testResults.push({
            test: 'Reconnection Settings',
            passed: allMatch,
            details: {
                maxAttempts: { expected: expectedMaxAttempts, actual: actualMaxAttempts, passed: maxAttemptsMatch },
                delay: { expected: expectedDelay, actual: actualDelay, passed: delayMatch },
                duration: { expected: expectedDuration, actual: totalDuration, passed: durationMatch }
            }
        });
        
        return allMatch;
    }

    // محاكاة نظام التحذيرات الجديد
    async simulateWarningSystem() {
        console.log('\n⚠️ محاكاة نظام التحذيرات المحدث...');
        
        const testBotData = {
            id: 999,
            bot_name: 'TestWarningBot',
            server_host: 'test.server.com',
            server_port: 25565,
            user_id: 123456789
        };
        
        console.log('📋 النظام المحدث:');
        console.log('   • إعادة الاتصال: كل 20 ثانية لمدة 5 دقائق (15 محاولة)');
        console.log('   • التحذيرات: أول رسالة فوراً، ثم كل دقيقة (5 رسائل إجمالية)');
        
        // محاكاة التحذيرات
        const expectedWarnings = [
            { alertCount: 1, timeRemaining: 4, timing: 'فوراً' },
            { alertCount: 2, timeRemaining: 3, timing: 'بعد دقيقة' },
            { alertCount: 3, timeRemaining: 2, timing: 'بعد دقيقتين' },
            { alertCount: 4, timeRemaining: 1, timing: 'بعد 3 دقائق' },
            { alertCount: 5, timeRemaining: 0, timing: 'بعد 4 دقائق (الأخير)' }
        ];
        
        console.log('\n📤 محاكاة إرسال التحذيرات:');
        
        for (const warning of expectedWarnings) {
            console.log(`   ${warning.alertCount}. التحذير ${warning.alertCount}/5 - ${warning.timing}`);
            console.log(`      ⏰ الوقت المتبقي: ${warning.timeRemaining} دقيقة`);
            
            // محاكاة إرسال التحذير
            this.botManager.emit('serverDown', {
                botId: testBotData.id,
                botName: testBotData.bot_name,
                host: testBotData.server_host,
                port: testBotData.server_port,
                alertCount: warning.alertCount,
                userId: testBotData.user_id,
                timeRemaining: warning.timeRemaining
            });
        }
        
        console.log('\n✅ تم محاكاة جميع التحذيرات بنجاح');
        
        this.testResults.push({
            test: 'Warning System Simulation',
            passed: true,
            details: {
                totalWarnings: expectedWarnings.length,
                warningInterval: '60 ثانية (دقيقة)',
                reconnectInterval: '20 ثانية',
                totalDuration: '5 دقائق'
            }
        });
        
        return true;
    }

    // اختبار توقيت النظام
    testSystemTiming() {
        console.log('\n⏰ اختبار توقيت النظام...');
        
        const reconnectInterval = 20; // ثانية
        const warningInterval = 60; // ثانية
        const totalDuration = 5 * 60; // 5 دقائق بالثواني
        
        const expectedReconnectAttempts = Math.floor(totalDuration / reconnectInterval);
        const expectedWarnings = Math.floor(totalDuration / warningInterval) + 1; // +1 للتحذير الأول
        
        console.log(`   📊 التوقيت المحسوب:`);
        console.log(`      🔄 محاولات إعادة الاتصال: ${expectedReconnectAttempts} محاولة (كل ${reconnectInterval} ثانية)`);
        console.log(`      ⚠️ عدد التحذيرات: ${expectedWarnings} تحذير (كل ${warningInterval} ثانية + الأول فوراً)`);
        console.log(`      ⏱️ المدة الإجمالية: ${totalDuration / 60} دقائق`);
        
        const reconnectMatch = expectedReconnectAttempts === 15;
        const warningMatch = expectedWarnings === 5;
        
        console.log(`   ${reconnectMatch ? '✅' : '❌'} محاولات إعادة الاتصال صحيحة: ${expectedReconnectAttempts}`);
        console.log(`   ${warningMatch ? '✅' : '❌'} عدد التحذيرات صحيح: ${expectedWarnings}`);
        
        const timingCorrect = reconnectMatch && warningMatch;
        
        this.testResults.push({
            test: 'System Timing',
            passed: timingCorrect,
            details: {
                reconnectAttempts: { expected: 15, calculated: expectedReconnectAttempts, passed: reconnectMatch },
                warnings: { expected: 5, calculated: expectedWarnings, passed: warningMatch }
            }
        });
        
        return timingCorrect;
    }

    // اختبار منطق النظام
    testSystemLogic() {
        console.log('\n🧠 اختبار منطق النظام...');
        
        const systemLogic = {
            reconnectEvery: 20, // ثانية
            warningEvery: 60, // ثانية  
            totalDuration: 300, // 5 دقائق بالثواني
            firstWarningImmediate: true,
            stopAfterDuration: true
        };
        
        console.log('   📋 منطق النظام:');
        console.log(`      1. إرسال التحذير الأول فوراً ✅`);
        console.log(`      2. إعادة محاولة الاتصال كل ${systemLogic.reconnectEvery} ثانية ✅`);
        console.log(`      3. إرسال تحذير كل ${systemLogic.warningEvery} ثانية ✅`);
        console.log(`      4. إيقاف البوت بعد ${systemLogic.totalDuration / 60} دقائق ✅`);
        console.log(`      5. إجمالي التحذيرات: 5 (1 فوري + 4 كل دقيقة) ✅`);
        console.log(`      6. إجمالي محاولات الاتصال: 15 (كل 20 ثانية لمدة 5 دقائق) ✅`);
        
        this.testResults.push({
            test: 'System Logic',
            passed: true,
            details: systemLogic
        });
        
        return true;
    }

    // عرض نتائج الاختبار
    displayResults() {
        console.log('\n📊 نتائج اختبار نظام التحذيرات المحدث:');
        console.log('=' .repeat(70));
        
        let passedTests = 0;
        let totalTests = this.testResults.length;
        
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${index + 1}. ${result.test}: ${status}`);
            
            if (result.details) {
                console.log(`   التفاصيل: ${JSON.stringify(result.details, null, 2).replace(/\n/g, '\n   ')}`);
            }
            
            if (result.passed) passedTests++;
        });
        
        console.log('=' .repeat(70));
        console.log(`📈 النتيجة النهائية: ${passedTests}/${totalTests} اختبار نجح`);
        
        if (passedTests === totalTests) {
            console.log('🎉 جميع الاختبارات نجحت! نظام التحذيرات المحدث يعمل بشكل مثالي.');
            console.log('\n🎯 ملخص النظام المحدث:');
            console.log('   ✅ إعادة الاتصال: كل 20 ثانية لمدة 5 دقائق (15 محاولة)');
            console.log('   ✅ التحذيرات: أول رسالة فوراً، ثم كل دقيقة (5 رسائل إجمالية)');
            console.log('   ✅ التوقيت: دقيق ومحسوب بعناية');
            console.log('   ✅ المنطق: سليم ومتسق');
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. راجع التفاصيل أعلاه.');
        }
        
        return passedTests === totalTests;
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        try {
            await this.init();
            
            console.log('🚀 بدء اختبار نظام التحذيرات المحدث...');
            console.log('🔧 التحديث المطلوب:');
            console.log('   • إعادة الاتصال: كل 20 ثانية لمدة 5 دقائق');
            console.log('   • التحذيرات: أول رسالة فوراً، ثم كل دقيقة');
            
            this.testReconnectionSettings();
            await this.simulateWarningSystem();
            this.testSystemTiming();
            this.testSystemLogic();
            
            const allPassed = this.displayResults();
            
            if (allPassed) {
                console.log('\n🚀 النظام جاهز للاستخدام مع التحديث الجديد!');
            }
            
            return allPassed;
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل الاختبارات:', error);
            return false;
        }
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const test = new UpdatedWarningSystemTest();
    test.runAllTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ فشل الاختبار:', error);
        process.exit(1);
    });
}

module.exports = UpdatedWarningSystemTest;
