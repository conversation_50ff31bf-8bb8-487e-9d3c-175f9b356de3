// إعدادات النظام
const config = {
    // إعدادات بوت التلغرام
    telegram: {
        // توكن البوت من متغيرات البيئة
        token: process.env.TELEGRAM_BOT_TOKEN,

        // إعدادات الرسائل
        messages: {
            maxLength: 4096, // الحد الأقصى لطول الرسالة في التلغرام
            parseMode: 'Markdown'
        },

        // إعدادات الأمان
        security: {
            rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
            maxRequestsPerWindow: parseInt(process.env.MAX_REQUESTS_PER_WINDOW) || 30,
            adminOnly: process.env.ADMIN_ONLY === 'true' || false
        }
    },

    // إعدادات البوتات
    bots: {
        // الحد الأقصى لعدد البوتات لكل مستخدم
        maxBotsPerUser: parseInt(process.env.MAX_BOTS_PER_USER) || 3,

        // إعدادات إعادة الاتصال - محدثة للنظام الجديد
        reconnection: {
            maxAttempts: parseInt(process.env.MAX_RECONNECT_ATTEMPTS) || 15, // 15 محاولة (5 دقائق × 3 محاولات/دقيقة)
            delay: parseInt(process.env.RECONNECT_DELAY) || 20000, // 20 ثانية
            backoffMultiplier: parseFloat(process.env.BACKOFF_MULTIPLIER) || 1.0 // تأخير ثابت
        },

        // مهلة الاتصال
        timeout: {
            connection: parseInt(process.env.CONNECTION_TIMEOUT) || 30000,
            keepAlive: parseInt(process.env.KEEP_ALIVE_TIMEOUT) || 30000,
            response: parseInt(process.env.RESPONSE_TIMEOUT) || 10000
        },

        // إعدادات الأداء
        performance: {
            maxMemoryUsage: parseInt(process.env.MAX_MEMORY_USAGE) || 512 * 1024 * 1024,
            gcInterval: parseInt(process.env.GC_INTERVAL) || 300000,
            statsUpdateInterval: parseInt(process.env.STATS_UPDATE_INTERVAL) || 60000
        }
    },

    // الإصدارات المدعومة - محدثة لآخر الإصدارات
    supportedVersions: {
        java: [
            '1.21.4',    // أحدث إصدار
            '1.21.3',
            '1.21.1',
            '1.21.0',
            '1.20.6'     // الإصدار الخامس
        ],
        bedrock: [
            '1.21.93',   // أحدث إصدار
            '1.21.92',
            '1.21.90',
            '1.21.80',   // الإصدار الرابع
            '1.20.30'    // الإصدار القديم المطلوب
        ]
    },

    // إعدادات قاعدة البيانات
    database: {
        // URL قاعدة البيانات من متغيرات البيئة
        // يدعم جميع قواعد البيانات الشائعة
        url: process.env.DATABASE_URL ||
             process.env.POSTGRES_URL ||
             process.env.MYSQL_URL ||
             process.env.MARIADB_URL ||
             process.env.MSSQL_URL ||
             process.env.ORACLE_URL ||
             process.env.MONGODB_URL ||
             process.env.REDIS_URL ||
             'sqlite:./minecraft_bot.db',

        // إعدادات النسخ الاحتياطي
        backup: {
            enabled: process.env.BACKUP_ENABLED === 'true' || true,
            interval: parseInt(process.env.BACKUP_INTERVAL) || 24 * 60 * 60 * 1000,
            maxBackups: parseInt(process.env.MAX_BACKUPS) || 7,
            path: process.env.BACKUP_PATH || './backups/',

            // إعدادات النسخ الاحتياطي التلقائي
            autoBackup: {
                enabled: process.env.AUTO_BACKUP_ENABLED === 'true' || false,
                interval: parseInt(process.env.AUTO_BACKUP_INTERVAL) || 5 * 60 * 1000, // 5 دقائق
                sendToAdmin: process.env.AUTO_BACKUP_SEND_TO_ADMIN === 'true' || true,
                keepInChat: process.env.AUTO_BACKUP_KEEP_IN_CHAT === 'true' || true,
                maxAutoBackups: parseInt(process.env.MAX_AUTO_BACKUPS) || 20
            },

            // إعدادات ضغط النسخ الاحتياطي
            compression: {
                enabled: process.env.BACKUP_COMPRESSION === 'true' || true,
                level: parseInt(process.env.BACKUP_COMPRESSION_LEVEL) || 6 // 1-9
            },

            // إعدادات تشفير النسخ الاحتياطي
            encryption: {
                enabled: process.env.BACKUP_ENCRYPTION === 'true' || false, // معطل افتراضياً
                algorithm: 'aes-256-cbc',
                password: process.env.BACKUP_PASSWORD || null
            }
        },

        // إعدادات التحسين
        optimization: {
            vacuumInterval: parseInt(process.env.VACUUM_INTERVAL) || 7 * 24 * 60 * 60 * 1000,
            analyzeInterval: parseInt(process.env.ANALYZE_INTERVAL) || 24 * 60 * 60 * 1000,
            checkpointInterval: parseInt(process.env.CHECKPOINT_INTERVAL) || 60 * 60 * 1000
        }
    },

    // إعدادات السجلات
    logging: {
        // مستوى السجلات
        level: process.env.LOG_LEVEL || 'info',

        // حفظ السجلات في ملفات
        file: {
            enabled: process.env.LOG_TO_FILE === 'true' || true,
            path: './logs/',
            maxSize: parseInt(process.env.MAX_LOG_SIZE) || 10 * 1024 * 1024,
            maxFiles: parseInt(process.env.MAX_LOG_FILES) || 5,
            datePattern: 'YYYY-MM-DD'
        },

        // إعدادات سجلات البوتات
        bots: {
            logChat: true,
            logCommands: true,
            logConnections: true,
            logErrors: true
        }
    },

    // إعدادات الأمان
    security: {
        // تشفير البيانات الحساسة
        encryption: {
            enabled: false, // يمكن تفعيله لاحقاً
            algorithm: 'aes-256-gcm',
            keyLength: 32
        },
        
        // حماية من الهجمات
        protection: {
            maxFailedAttempts: 5, // عدد المحاولات الفاشلة المسموحة
            lockoutDuration: 15 * 60 * 1000, // مدة الحظر (15 دقيقة)
            ipWhitelist: [], // قائمة IP المسموحة
            ipBlacklist: [] // قائمة IP المحظورة
        }
    },

    // إعدادات الشبكة
    network: {
        // إعدادات البروكسي (اختياري)
        proxy: {
            enabled: false,
            host: '',
            port: 0,
            username: '',
            password: ''
        },
        
        // إعدادات DNS
        dns: {
            timeout: 5000,
            retries: 3,
            servers: ['*******', '*******']
        }
    },

    // إعدادات المراقبة
    monitoring: {
        // مراقبة الأداء
        performance: {
            enabled: process.env.PERFORMANCE_MONITORING === 'true' || true,
            interval: parseInt(process.env.MONITORING_INTERVAL) || 60000,
            metrics: ['cpu', 'memory', 'connections', 'errors']
        },

        // التنبيهات
        alerts: {
            enabled: process.env.ALERTS_ENABLED === 'true' || true,
            thresholds: {
                memoryUsage: parseInt(process.env.MEMORY_THRESHOLD) || 80,
                errorRate: parseInt(process.env.ERROR_RATE_THRESHOLD) || 10,
                connectionFailures: parseInt(process.env.CONNECTION_FAILURE_THRESHOLD) || 5
            }
        }
    },

    // إعدادات التطوير
    development: {
        // وضع التطوير
        debug: process.env.DEBUG_MODE === 'true' || process.env.NODE_ENV === 'development',

        // إعادة التحميل التلقائي
        hotReload: process.env.HOT_RELOAD === 'true' || false,

        // اختبار الاتصالات
        testMode: process.env.TEST_MODE === 'true' || false,

        // محاكاة الأخطاء
        simulateErrors: process.env.SIMULATE_ERRORS === 'true' || false
    },

    // إعدادات الإنتاج
    production: {
        // تحسينات الأداء
        optimizations: {
            enableGzip: process.env.ENABLE_GZIP === 'true' || true,
            minifyResponses: process.env.MINIFY_RESPONSES === 'true' || true,
            cacheStatic: process.env.CACHE_STATIC === 'true' || true
        },

        // إعدادات الكلاستر
        cluster: {
            enabled: process.env.CLUSTER_ENABLED === 'true' || false,
            workers: require('os').cpus().length
        }
    },

    // رسائل النظام
    messages: {
        // رسائل الترحيب
        welcome: {
            ar: '🎮 مرحباً بك في بوت ماينكرافت المتقدم!',
            en: '🎮 Welcome to Advanced Minecraft Bot!'
        },
        
        // رسائل الأخطاء
        errors: {
            general: 'حدث خطأ، يرجى المحاولة مرة أخرى',
            connection: 'فشل في الاتصال بالسيرفر',
            permission: 'ليس لديك صلاحية لهذا الإجراء',
            notFound: 'العنصر المطلوب غير موجود',
            rateLimit: 'تم تجاوز الحد المسموح من الطلبات'
        },
        
        // رسائل النجاح
        success: {
            botCreated: 'تم إنشاء البوت بنجاح!',
            botStarted: 'تم تشغيل البوت بنجاح!',
            botStopped: 'تم إيقاف البوت بنجاح!',
            messageSent: 'تم إرسال الرسالة بنجاح!',
            commandExecuted: 'تم تنفيذ الأمر بنجاح!'
        }
    }
};

// دالة للحصول على إعداد معين
function getConfig(path) {
    return path.split('.').reduce((obj, key) => obj && obj[key], config);
}

// دالة لتحديث إعداد معين
function setConfig(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, config);
    target[lastKey] = value;
}

// دالة للتحقق من صحة الإعدادات
function validateConfig() {
    const errors = [];
    
    // التحقق من التوكن
    if (!config.telegram.token || config.telegram.token === 'YOUR_BOT_TOKEN_HERE') {
        errors.push('توكن بوت التلغرام غير صحيح');
    }
    
    // التحقق من الإصدارات المدعومة
    if (!config.supportedVersions.java.length || !config.supportedVersions.bedrock.length) {
        errors.push('قائمة الإصدارات المدعومة فارغة');
    }
    
    // التحقق من إعدادات قاعدة البيانات
    if (!config.database.url) {
        errors.push('رابط قاعدة البيانات غير محدد');
    }
    
    return {
        valid: errors.length === 0,
        errors
    };
}

module.exports = {
    config,
    getConfig,
    setConfig,
    validateConfig
};
