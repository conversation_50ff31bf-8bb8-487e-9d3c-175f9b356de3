#!/usr/bin/env node

/**
 * اختبار شامل لجميع مكونات النظام
 */

require('dotenv').config();
const Database = require('./database');
const BackupManager = require('./backup-manager');
const securityManager = require('./security');
const { config, validateConfig } = require('./config');

class CompleteSystemTester {
    constructor() {
        this.testResults = [];
        this.db = null;
        this.backupManager = null;
        this.securityManager = securityManager;
    }

    async runAllTests() {
        console.log('🧪 بدء الاختبار الشامل للنظام...\n');

        // اختبار الإعدادات
        await this.testConfiguration();
        
        // اختبار قاعدة البيانات
        await this.testDatabase();
        
        // اختبار نظام الأمان
        await this.testSecurity();
        
        // اختبار نظام النسخ الاحتياطي
        await this.testBackupSystem();
        
        // اختبار التكامل
        await this.testIntegration();

        this.printResults();
        this.generateReport();
    }

    async testConfiguration() {
        console.log('1️⃣ اختبار الإعدادات...');
        
        try {
            // اختبار تحميل الإعدادات
            const validation = validateConfig();
            
            if (validation.valid) {
                console.log('   ✅ الإعدادات صحيحة');
                this.testResults.push({ test: 'تحميل الإعدادات', status: 'نجح' });
            } else {
                console.log('   ⚠️ مشاكل في الإعدادات:');
                validation.errors.forEach(error => {
                    console.log(`      - ${error}`);
                });
                this.testResults.push({ test: 'تحميل الإعدادات', status: 'تحذير', errors: validation.errors });
            }

            // اختبار الإصدارات المدعومة
            const javaVersions = config.supportedVersions.java;
            const bedrockVersions = config.supportedVersions.bedrock;
            
            if (javaVersions.length > 0 && bedrockVersions.length > 0) {
                console.log(`   ✅ الإصدارات المدعومة: Java(${javaVersions.length}), Bedrock(${bedrockVersions.length})`);
                this.testResults.push({ test: 'الإصدارات المدعومة', status: 'نجح' });
            } else {
                console.log('   ❌ لا توجد إصدارات مدعومة');
                this.testResults.push({ test: 'الإصدارات المدعومة', status: 'فشل' });
            }

        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'تحميل الإعدادات', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testDatabase() {
        console.log('2️⃣ اختبار قاعدة البيانات...');
        
        try {
            // تهيئة قاعدة البيانات
            this.db = await new Database().init();
            console.log('   ✅ تم تهيئة قاعدة البيانات');
            this.testResults.push({ test: 'تهيئة قاعدة البيانات', status: 'نجح' });

            // اختبار إنشاء مستخدم
            const testUserId = 123456789;
            await this.db.createUser(testUserId, 'TestUser');
            console.log('   ✅ تم إنشاء مستخدم تجريبي');
            
            // اختبار جلب المستخدم
            const user = await this.db.getUser(testUserId);
            if (user && user.telegram_id === testUserId) {
                console.log('   ✅ تم جلب بيانات المستخدم');
                this.testResults.push({ test: 'عمليات المستخدمين', status: 'نجح' });
            } else {
                console.log('   ❌ فشل في جلب بيانات المستخدم');
                this.testResults.push({ test: 'عمليات المستخدمين', status: 'فشل' });
            }

            // اختبار إنشاء بوت
            const botId = await this.db.createBot(testUserId, 'TestBot', 'localhost', 25565, 'TestPlayer', 'java', '1.20.1');
            if (botId) {
                console.log('   ✅ تم إنشاء بوت تجريبي');
                this.testResults.push({ test: 'عمليات البوتات', status: 'نجح' });
            } else {
                console.log('   ❌ فشل في إنشاء البوت');
                this.testResults.push({ test: 'عمليات البوتات', status: 'فشل' });
            }

            // اختبار الإعدادات
            await this.db.setSetting('test_setting', 'test_value');
            const settingValue = await this.db.getSetting('test_setting');
            if (settingValue === 'test_value') {
                console.log('   ✅ تم اختبار الإعدادات');
                this.testResults.push({ test: 'إدارة الإعدادات', status: 'نجح' });
            } else {
                console.log('   ❌ فشل في اختبار الإعدادات');
                this.testResults.push({ test: 'إدارة الإعدادات', status: 'فشل' });
            }

        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'قاعدة البيانات', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testSecurity() {
        console.log('3️⃣ اختبار نظام الأمان...');

        try {
            // استخدام المثيل الموجود
            
            // اختبار معدل الطلبات
            const userId = 123456789;
            let rateLimitPassed = true;
            
            // محاولة 35 طلب (أكثر من الحد المسموح 30)
            for (let i = 0; i < 35; i++) {
                if (!this.securityManager.checkRateLimit(userId)) {
                    rateLimitPassed = false;
                    break;
                }
            }
            
            if (!rateLimitPassed) {
                console.log('   ✅ نظام معدل الطلبات يعمل بشكل صحيح');
                this.testResults.push({ test: 'معدل الطلبات', status: 'نجح' });
            } else {
                console.log('   ❌ نظام معدل الطلبات لا يعمل');
                this.testResults.push({ test: 'معدل الطلبات', status: 'فشل' });
            }

            // اختبار تسجيل المحاولات الفاشلة
            const isBlocked = this.securityManager.recordFailedAttempt(userId);
            console.log(`   ✅ تم اختبار تسجيل المحاولات الفاشلة: ${isBlocked ? 'محظور' : 'غير محظور'}`);
            this.testResults.push({ test: 'المحاولات الفاشلة', status: 'نجح' });

            // اختبار التشفير (إذا كان مفعلاً)
            if (config.security.encryption.enabled) {
                const testData = 'test data';
                const encrypted = this.securityManager.encrypt(testData);
                const decrypted = this.securityManager.decrypt(encrypted);
                
                if (decrypted === testData) {
                    console.log('   ✅ نظام التشفير يعمل بشكل صحيح');
                    this.testResults.push({ test: 'التشفير', status: 'نجح' });
                } else {
                    console.log('   ❌ نظام التشفير لا يعمل');
                    this.testResults.push({ test: 'التشفير', status: 'فشل' });
                }
            } else {
                console.log('   ⚠️ التشفير غير مفعل');
                this.testResults.push({ test: 'التشفير', status: 'تخطي' });
            }

        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'نظام الأمان', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testBackupSystem() {
        console.log('4️⃣ اختبار نظام النسخ الاحتياطي...');
        
        try {
            if (!this.db) {
                throw new Error('قاعدة البيانات غير مهيئة');
            }

            this.backupManager = new BackupManager(this.db, null);
            
            // اختبار إنشاء نسخة احتياطية
            const backup = await this.backupManager.createBackup(true);
            if (backup.success) {
                console.log(`   ✅ تم إنشاء نسخة احتياطية: ${backup.fileName}`);
                this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'نجح' });
            } else {
                console.log(`   ❌ فشل في إنشاء النسخة الاحتياطية: ${backup.error}`);
                this.testResults.push({ test: 'إنشاء النسخة الاحتياطية', status: 'فشل', error: backup.error });
            }

            // اختبار قائمة النسخ الاحتياطية
            const backups = await this.backupManager.listBackups();
            console.log(`   ✅ عدد النسخ الاحتياطية: ${backups.length}`);
            this.testResults.push({ test: 'قائمة النسخ الاحتياطية', status: 'نجح' });

            // اختبار حالة النسخ الاحتياطي
            const status = this.backupManager.getBackupStatus();
            console.log(`   ✅ حالة النسخ التلقائي: ${status.autoBackupEnabled ? 'مفعل' : 'معطل'}`);
            this.testResults.push({ test: 'حالة النسخ الاحتياطي', status: 'نجح' });

            // اختبار النسخ التلقائي
            const startResult = this.backupManager.startAutoBackup();
            const stopResult = this.backupManager.stopAutoBackup();
            
            if (startResult && stopResult) {
                console.log('   ✅ نظام النسخ التلقائي يعمل بشكل صحيح');
                this.testResults.push({ test: 'النسخ التلقائي', status: 'نجح' });
            } else {
                console.log('   ❌ مشكلة في نظام النسخ التلقائي');
                this.testResults.push({ test: 'النسخ التلقائي', status: 'فشل' });
            }

        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'نظام النسخ الاحتياطي', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    async testIntegration() {
        console.log('5️⃣ اختبار التكامل...');
        
        try {
            // اختبار التكامل بين المكونات
            if (this.db && this.backupManager && this.securityManager) {
                console.log('   ✅ جميع المكونات مهيئة بشكل صحيح');
                this.testResults.push({ test: 'تهيئة المكونات', status: 'نجح' });
            } else {
                console.log('   ❌ بعض المكونات غير مهيئة');
                this.testResults.push({ test: 'تهيئة المكونات', status: 'فشل' });
            }

            // اختبار الذاكرة
            const memoryUsage = process.memoryUsage();
            const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
            console.log(`   📊 استخدام الذاكرة: ${memoryMB} MB`);
            
            if (memoryMB < 100) {
                console.log('   ✅ استخدام الذاكرة ضمن الحدود الطبيعية');
                this.testResults.push({ test: 'استخدام الذاكرة', status: 'نجح' });
            } else {
                console.log('   ⚠️ استخدام الذاكرة مرتفع');
                this.testResults.push({ test: 'استخدام الذاكرة', status: 'تحذير' });
            }

        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            this.testResults.push({ test: 'التكامل', status: 'خطأ', error: error.message });
        }
        
        console.log('');
    }

    printResults() {
        console.log('📊 نتائج الاختبار الشامل:\n');
        
        let passed = 0;
        let failed = 0;
        let warnings = 0;
        let errors = 0;
        let skipped = 0;
        
        this.testResults.forEach((result, index) => {
            let statusIcon = '';
            switch (result.status) {
                case 'نجح':
                    statusIcon = '✅';
                    passed++;
                    break;
                case 'فشل':
                    statusIcon = '❌';
                    failed++;
                    break;
                case 'تحذير':
                    statusIcon = '⚠️';
                    warnings++;
                    break;
                case 'خطأ':
                    statusIcon = '💥';
                    errors++;
                    break;
                case 'تخطي':
                    statusIcon = '⏭️';
                    skipped++;
                    break;
            }
            
            console.log(`${index + 1}. ${statusIcon} ${result.test}: ${result.status}`);
            if (result.error) {
                console.log(`   📝 التفاصيل: ${result.error}`);
            }
            if (result.errors && result.errors.length > 0) {
                result.errors.forEach(error => {
                    console.log(`   📝 ${error}`);
                });
            }
        });
        
        console.log('\n📈 الملخص:');
        console.log(`   ✅ نجح: ${passed}`);
        console.log(`   ❌ فشل: ${failed}`);
        console.log(`   ⚠️ تحذيرات: ${warnings}`);
        console.log(`   💥 أخطاء: ${errors}`);
        console.log(`   ⏭️ تخطي: ${skipped}`);
        
        const total = passed + failed + warnings + errors + skipped;
        const successRate = Math.round((passed / total) * 100);
        
        console.log(`\n📊 معدل النجاح: ${successRate}%`);
        
        if (failed === 0 && errors === 0) {
            console.log('\n🎉 جميع الاختبارات الأساسية نجحت! النظام جاهز للاستخدام.');
        } else {
            console.log('\n🚫 بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        }
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalTests: this.testResults.length,
            passed: this.testResults.filter(r => r.status === 'نجح').length,
            failed: this.testResults.filter(r => r.status === 'فشل').length,
            warnings: this.testResults.filter(r => r.status === 'تحذير').length,
            errors: this.testResults.filter(r => r.status === 'خطأ').length,
            skipped: this.testResults.filter(r => r.status === 'تخطي').length,
            results: this.testResults
        };

        console.log('\n📄 تم إنشاء تقرير الاختبار');
        console.log('💾 يمكن حفظ التقرير في ملف JSON للمراجعة اللاحقة');
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const tester = new CompleteSystemTester();
    tester.runAllTests().catch(error => {
        console.error('❌ خطأ في تشغيل الاختبارات:', error.message);
        process.exit(1);
    });
}

module.exports = CompleteSystemTester;
