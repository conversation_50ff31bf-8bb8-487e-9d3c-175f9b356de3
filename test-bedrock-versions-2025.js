const Database = require('./database');
const BotManager = require('./bot-manager');
const { config } = require('./config');

/**
 * اختبار شامل للإصدارات الجديدة لـ Bedrock
 * يتحقق من جميع الملفات والإعدادات
 */
class BedrockVersionsTest2025 {
    constructor() {
        this.db = null;
        this.botManager = null;
        this.testResults = [];
        this.expectedVersions = ['1.21.93', '1.21.92', '1.21.90', '1.21.80', '1.20.30'];
    }

    async init() {
        console.log('🔧 تهيئة اختبار إصدارات Bedrock الجديدة...');
        this.db = await new Database().init();
        this.botManager = await new BotManager().init();
        console.log('✅ تم تهيئة النظام للاختبار');
    }

    // اختبار إصدارات config.js
    testConfigVersions() {
        console.log('\n📋 اختبار إصدارات config.js...');
        
        const actualVersions = config.supportedVersions.bedrock;
        const versionsMatch = JSON.stringify(this.expectedVersions) === JSON.stringify(actualVersions);
        
        this.testResults.push({
            test: 'Config.js Bedrock Versions',
            passed: versionsMatch,
            expected: this.expectedVersions,
            actual: actualVersions
        });
        
        console.log(`✅ Config Versions: ${versionsMatch ? 'PASS' : 'FAIL'}`);
        if (versionsMatch) {
            actualVersions.forEach((version, index) => {
                console.log(`   ${index + 1}. ${version}`);
            });
        }
        
        return versionsMatch;
    }

    // اختبار إصدارات bot-manager.js
    testBotManagerVersions() {
        console.log('\n🤖 اختبار إصدارات bot-manager.js...');
        
        const actualVersions = this.botManager.supportedVersions.bedrock;
        const versionsMatch = JSON.stringify(this.expectedVersions) === JSON.stringify(actualVersions);
        
        this.testResults.push({
            test: 'BotManager Bedrock Versions',
            passed: versionsMatch,
            expected: this.expectedVersions,
            actual: actualVersions
        });
        
        console.log(`✅ BotManager Versions: ${versionsMatch ? 'PASS' : 'FAIL'}`);
        if (versionsMatch) {
            actualVersions.forEach((version, index) => {
                console.log(`   ${index + 1}. ${version}`);
            });
        }
        
        return versionsMatch;
    }

    // اختبار البوتات المحدثة في قاعدة البيانات
    async testDatabaseBots() {
        console.log('\n🗄️ اختبار البوتات المحدثة في قاعدة البيانات...');
        
        try {
            const bots = await this.db.getAllBots();
            const bedrockBots = bots.filter(bot => bot.edition === 'bedrock');
            
            let allBotsUpdated = true;
            const botVersions = [];
            
            for (const bot of bedrockBots) {
                const isSupported = this.expectedVersions.includes(bot.minecraft_version);
                botVersions.push({
                    name: bot.bot_name,
                    version: bot.minecraft_version,
                    supported: isSupported
                });
                
                if (!isSupported) {
                    allBotsUpdated = false;
                }
                
                console.log(`   ${isSupported ? '✅' : '❌'} ${bot.bot_name}: ${bot.minecraft_version}`);
            }
            
            this.testResults.push({
                test: 'Database Bots Updated',
                passed: allBotsUpdated,
                expected: 'All bots using supported versions',
                actual: `${botVersions.filter(b => b.supported).length}/${botVersions.length} bots updated`
            });
            
            console.log(`✅ Database Bots: ${allBotsUpdated ? 'PASS' : 'FAIL'}`);
            console.log(`📊 إجمالي بوتات Bedrock: ${bedrockBots.length}`);
            
            return allBotsUpdated;
            
        } catch (error) {
            console.error(`❌ خطأ في اختبار قاعدة البيانات: ${error.message}`);
            this.testResults.push({
                test: 'Database Bots Updated',
                passed: false,
                expected: 'All bots using supported versions',
                actual: `Error: ${error.message}`
            });
            return false;
        }
    }

    // اختبار إنشاء بوت جديد بالإصدارات الجديدة
    async testBotCreation() {
        console.log('\n🆕 اختبار إنشاء بوت جديد بالإصدارات الجديدة...');
        
        try {
            // محاولة إنشاء بوت تجريبي بأحدث إصدار
            const testBotConfig = {
                edition: 'bedrock',
                version: '1.21.93',
                host: 'test.server.com',
                port: 19132,
                username: 'TestBot2025',
                name: 'Test Bot 2025'
            };
            
            // التحقق من صحة الإعدادات
            const validation = this.botManager.validateBotConfig(testBotConfig);
            
            this.testResults.push({
                test: 'Bot Creation Validation',
                passed: validation.valid,
                expected: 'Valid bot configuration',
                actual: validation.valid ? 'Valid' : validation.error
            });
            
            console.log(`✅ Bot Creation: ${validation.valid ? 'PASS' : 'FAIL'}`);
            if (!validation.valid) {
                console.log(`   ❌ خطأ: ${validation.error}`);
            }
            
            return validation.valid;
            
        } catch (error) {
            console.error(`❌ خطأ في اختبار إنشاء البوت: ${error.message}`);
            this.testResults.push({
                test: 'Bot Creation Validation',
                passed: false,
                expected: 'Valid bot configuration',
                actual: `Error: ${error.message}`
            });
            return false;
        }
    }

    // اختبار التوافق مع الإصدارات
    testVersionCompatibility() {
        console.log('\n🔄 اختبار التوافق مع الإصدارات...');
        
        const compatibilityTests = [
            { version: '1.21.93', shouldPass: true, description: 'أحدث إصدار' },
            { version: '1.21.92', shouldPass: true, description: 'إصدار مدعوم' },
            { version: '1.21.90', shouldPass: true, description: 'إصدار مدعوم' },
            { version: '1.21.80', shouldPass: true, description: 'إصدار مدعوم' },
            { version: '1.20.30', shouldPass: true, description: 'الإصدار القديم المطلوب' },
            { version: '1.21.50', shouldPass: false, description: 'إصدار قديم غير مدعوم' },
            { version: '1.21.44', shouldPass: false, description: 'إصدار قديم غير مدعوم' }
        ];
        
        let allTestsPassed = true;
        
        for (const test of compatibilityTests) {
            const isSupported = this.expectedVersions.includes(test.version);
            const testPassed = isSupported === test.shouldPass;
            
            if (!testPassed) {
                allTestsPassed = false;
            }
            
            console.log(`   ${testPassed ? '✅' : '❌'} ${test.version}: ${test.description} - ${isSupported ? 'مدعوم' : 'غير مدعوم'}`);
        }
        
        this.testResults.push({
            test: 'Version Compatibility',
            passed: allTestsPassed,
            expected: 'All compatibility tests pass',
            actual: allTestsPassed ? 'All tests passed' : 'Some tests failed'
        });
        
        console.log(`✅ Version Compatibility: ${allTestsPassed ? 'PASS' : 'FAIL'}`);
        return allTestsPassed;
    }

    // عرض نتائج الاختبار
    displayResults() {
        console.log('\n📊 نتائج اختبار إصدارات Bedrock الجديدة:');
        console.log('=' .repeat(60));
        
        let passedTests = 0;
        let totalTests = this.testResults.length;
        
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${index + 1}. ${result.test}: ${status}`);
            
            if (!result.passed) {
                console.log(`   Expected: ${JSON.stringify(result.expected)}`);
                console.log(`   Actual: ${JSON.stringify(result.actual)}`);
            }
            
            if (result.passed) passedTests++;
        });
        
        console.log('=' .repeat(60));
        console.log(`📈 النتيجة النهائية: ${passedTests}/${totalTests} اختبار نجح`);
        
        if (passedTests === totalTests) {
            console.log('🎉 جميع الاختبارات نجحت! إصدارات Bedrock محدثة بالكامل.');
            console.log('\n🎯 الإصدارات المدعومة الآن:');
            this.expectedVersions.forEach((version, index) => {
                console.log(`   ${index + 1}. ${version}${index === 0 ? ' (أحدث إصدار)' : ''}${version === '1.20.30' ? ' (الإصدار القديم المطلوب)' : ''}`);
            });
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        }
        
        return passedTests === totalTests;
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        try {
            await this.init();
            
            console.log('🚀 بدء اختبار إصدارات Bedrock الجديدة...');
            console.log('📋 الإصدارات المطلوبة: 1.21.93, 1.21.92, 1.21.90, 1.21.80, 1.20.30');
            
            const configTest = this.testConfigVersions();
            const botManagerTest = this.testBotManagerVersions();
            const databaseTest = await this.testDatabaseBots();
            const creationTest = await this.testBotCreation();
            const compatibilityTest = this.testVersionCompatibility();
            
            const allPassed = this.displayResults();
            
            if (allPassed) {
                console.log('\n🎯 التوصيات:');
                console.log('✅ جميع إصدارات Bedrock محدثة بنجاح');
                console.log('✅ النظام جاهز لاستخدام الإصدارات الجديدة');
                console.log('✅ يمكن إنشاء بوتات جديدة بالإصدارات المحدثة');
                console.log('✅ البوتات الموجودة تم تحديثها تلقائياً');
            }
            
            return allPassed;
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل الاختبارات:', error);
            return false;
        }
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const test = new BedrockVersionsTest2025();
    test.runAllTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ فشل الاختبار:', error);
        process.exit(1);
    });
}

module.exports = BedrockVersionsTest2025;
