# استخدام Node.js 22 LTS كصورة أساسية
FROM node:22-alpine

# تعيين متغير البيئة للإنتاج
ENV NODE_ENV=production

# تعيين متغيرات البيئة لبناء التبعيات الأصلية
ENV PYTHON=/usr/bin/python3
ENV npm_config_build_from_source=true

# إنشاء مجلد العمل
WORKDIR /app

# تثبيت الأدوات المطلوبة لبناء التبعيات الأصلية
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    gcc \
    cmake \
    git \
    libc6-compat \
    sqlite \
    postgresql-client \
    mysql-client \
    mariadb-connector-c \
    && ln -sf python3 /usr/bin/python

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات مع إعادة المحاولة في حالة الفشل
RUN npm install --only=production --verbose || \
    (npm cache clean --force && npm install --only=production --verbose) && \
    npm cache clean --force

# نسخ باقي ملفات المشروع
COPY . .

# إنشاء مجلدات مطلوبة
RUN mkdir -p logs backups

# تعيين الصلاحيات المناسبة
RUN chown -R node:node /app

# التبديل إلى مستخدم غير root
USER node

# كشف المنفذ
EXPOSE 3000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node -e "const http=require('http');const port=process.env.PORT||3000;const req=http.request({hostname:'localhost',port,path:'/health',timeout:5000},(res)=>{process.exit(res.statusCode===200?0:1)});req.on('error',()=>process.exit(1));req.end();" || exit 1

# أمر بدء التشغيل
CMD ["npm", "start"]

# إضافة تسميات للصورة
LABEL maintainer="Minecraft Bot System Developer"
LABEL description="نظام متكامل لإنشاء وإدارة بوتات ماينكرافت Java و Bedrock عبر بوت التلغرام"
LABEL version="1.0.0"
