const mineflayer = require('mineflayer');
const { EventEmitter } = require('events');
const dns = require('dns');
const net = require('net');

class MinecraftJavaBot extends EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.bot = null;
        this.isConnected = false;
        this.connectionTime = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = parseInt(process.env.MAX_RECONNECT_ATTEMPTS) || 15; // 15 محاولة (5 دقائق × 3 محاولات/دقيقة)
        this.reconnectDelay = parseInt(process.env.RECONNECT_DELAY) || 20000; // 20 ثانية
        this.shouldReconnect = true;
        this.reconnectTimeout = null;
        this.connectionTimeout = parseInt(process.env.CONNECTION_TIMEOUT) || 30000;
        this.lastError = null;
        this.connectionAttempts = 0;

        // إعداد DNS للحصول على أفضل أداء
        dns.setServers(['*******', '*******', '**************']);
    }

    async connect() {
        try {
            this.connectionAttempts++;
            console.log(`🔄 محاولة الاتصال ${this.connectionAttempts} بسيرفر Java: ${this.config.host}:${this.config.port}`);

            // فحص الاتصال بالسيرفر أولاً
            const isReachable = await this.checkServerReachability();
            if (!isReachable) {
                throw new Error(`السيرفر ${this.config.host}:${this.config.port} غير قابل للوصول`);
            }

            // تحديد أفضل إصدار للاتصال
            const version = await this.detectBestVersion();

            const botOptions = {
                host: this.config.host,
                port: parseInt(this.config.port),
                username: this.config.username,
                version: version,
                auth: 'offline',
                hideErrors: false,
                checkTimeoutInterval: this.connectionTimeout,
                keepAlive: true,
                respawn: true,
                skipValidation: false, // نفعل التحقق للحصول على اتصال أفضل
                viewDistance: 'tiny', // تقليل استهلاك البيانات
                chatLengthLimit: 256,
                physicsEnabled: false, // تعطيل الفيزياء لتوفير الأداء
                loadInternalPlugins: false, // تعطيل الإضافات الداخلية

                // إعدادات الشبكة المحسنة
                connectTimeout: this.connectionTimeout,
                tcpNoDelay: true,
                keepAliveInitialDelay: 0,

                // معالج الاتصال
                connect: (client) => {
                    console.log(`🔗 بدء الاتصال مع ${this.config.host}:${this.config.port}`);

                    // إعداد timeout للاتصال
                    const timeout = setTimeout(() => {
                        console.log(`⏰ انتهت مهلة الاتصال مع ${this.config.host}:${this.config.port}`);
                        client.end();
                    }, this.connectionTimeout);

                    client.once('connect', () => {
                        clearTimeout(timeout);
                        console.log(`✅ تم تأسيس الاتصال مع ${this.config.host}:${this.config.port}`);
                    });
                }
            };

            // إضافة كلمة مرور إذا كانت متوفرة
            if (this.config.password) {
                botOptions.password = this.config.password;
                botOptions.auth = this.config.authType || 'microsoft';
            }

            this.bot = mineflayer.createBot(botOptions);
            this.setupEventHandlers();

        } catch (error) {
            console.error(`❌ خطأ في إنشاء البوت: ${error.message}`);
            this.lastError = error;
            this.emit('error', error);
            this.handleReconnect();
        }
    }

    // فحص إمكانية الوصول للسيرفر
    async checkServerReachability() {
        return new Promise((resolve) => {
            const socket = new net.Socket();
            const timeout = setTimeout(() => {
                socket.destroy();
                resolve(false);
            }, 5000);

            socket.connect(this.config.port, this.config.host, () => {
                clearTimeout(timeout);
                socket.destroy();
                resolve(true);
            });

            socket.on('error', () => {
                clearTimeout(timeout);
                resolve(false);
            });
        });
    }

    // اكتشاف أفضل إصدار للاتصال
    async detectBestVersion() {
        // قائمة الإصدارات مرتبة من الأحدث للأقدم - محدثة
        const versions = [
            '1.21.4', '1.21.3', '1.21.1', '1.21.0', '1.20.6',
            '1.19.4', '1.19.2', '1.18.2', '1.17.1', '1.16.5'
        ];

        // إذا كان الإصدار محدد في الإعدادات، استخدمه
        if (this.config.version && versions.includes(this.config.version)) {
            return this.config.version;
        }

        // جرب الإصدارات واحداً تلو الآخر
        for (const version of versions) {
            try {
                // محاولة سريعة لفحص الإصدار
                const testResult = await this.testVersion(version);
                if (testResult) {
                    console.log(`✅ تم اكتشاف الإصدار المناسب: ${version}`);
                    return version;
                }
            } catch (error) {
                continue;
            }
        }

        // إذا فشل كل شيء، استخدم الإصدار الافتراضي
        return this.config.version || '1.20.1';
    }

    // اختبار إصدار معين
    async testVersion(version) {
        return new Promise((resolve) => {
            try {
                // محاولة سريعة للاتصال بالإصدار
                const testBot = mineflayer.createBot({
                    host: this.config.host,
                    port: this.config.port,
                    username: `test_${Date.now()}`,
                    version: version,
                    auth: 'offline',
                    hideErrors: true,
                    connectTimeout: 3000
                });

                const timeout = setTimeout(() => {
                    testBot.quit();
                    resolve(false);
                }, 3000);

                testBot.once('login', () => {
                    clearTimeout(timeout);
                    testBot.quit();
                    resolve(true);
                });

                testBot.once('error', () => {
                    clearTimeout(timeout);
                    resolve(false);
                });

            } catch (error) {
                resolve(false);
            }
        });
    }

    setupEventHandlers() {
        // عند الاتصال بنجاح
        this.bot.on('login', () => {
            console.log(`✅ تم الاتصال بنجاح بسيرفر ${this.config.host}:${this.config.port}`);
            this.isConnected = true;
            this.connectionTime = new Date();
            this.reconnectAttempts = 0;
            this.emit('connected', {
                server: `${this.config.host}:${this.config.port}`,
                username: this.config.username,
                version: this.config.version
            });
        });

        // عند دخول العالم
        this.bot.on('spawn', () => {
            console.log(`🌍 دخل البوت ${this.config.username} إلى العالم`);

            // إرسال إشعار دخول العالم فوراً
            this.emit('enteredWorld', {
                server: `${this.config.host}:${this.config.port}`,
                username: this.config.username,
                position: this.bot.entity.position,
                health: this.bot.health,
                food: this.bot.food,
                gameMode: this.bot.game ? this.bot.game.gameMode : null,
                dimension: this.bot.game ? this.bot.game.dimension : null
            });

            this.emit('spawned', {
                position: this.bot.entity.position,
                health: this.bot.health,
                food: this.bot.food
            });
        });

        // عند استقبال رسالة في الشات
        this.bot.on('chat', (username, message) => {
            if (username === this.bot.username) return;
            
            console.log(`💬 ${username}: ${message}`);
            this.emit('chat', { username, message });
        });

        // عند حدوث خطأ
        this.bot.on('error', (err) => {
            this.isConnected = false;
            this.lastError = err;

            // تصنيف الأخطاء
            const errorType = this.classifyError(err);

            switch (errorType) {
                case 'CONNECTION_TIMEOUT':
                    console.log(`⏰ انتهت مهلة الاتصال مع ${this.config.host}:${this.config.port}`);
                    break;

                case 'CONNECTION_REFUSED':
                    console.log(`🚫 رفض الاتصال من ${this.config.host}:${this.config.port} - السيرفر مطفي أو غير متاح`);
                    break;

                case 'INVALID_VERSION':
                    console.log(`🔄 إصدار غير متوافق، جاري المحاولة بإصدار آخر...`);
                    break;

                case 'AUTH_ERROR':
                    console.error(`🔐 خطأ في المصادقة: ${err.message}`);
                    this.emit('error', err);
                    return; // لا نعيد الاتصال في حالة خطأ المصادقة

                case 'KICKED':
                    console.log(`👢 تم ركل البوت: ${err.message}`);
                    break;

                default:
                    console.error(`❌ خطأ في البوت: ${err.message}`);
                    this.emit('error', err);
            }

            // محاولة إعادة الاتصال
            this.handleReconnect();
        });

        // عند انقطاع الاتصال
        this.bot.on('end', (reason) => {
            console.log(`🔌 السيرفر ${this.config.host}:${this.config.port} مطفي أو غير متصل`);
            this.isConnected = false;
            this.emit('disconnected', {
                reason,
                connectionTime: this.connectionTime,
                disconnectionTime: new Date()
            });

            // محاولة إعادة الاتصال
            this.handleReconnect();
        });

        // عند الموت
        this.bot.on('death', () => {
            console.log(`💀 مات البوت ${this.config.username}`);
            this.emit('death', {
                position: this.bot.entity.position,
                killer: this.bot.lastDamageSource
            });
        });

        // عند الإحياء
        this.bot.on('respawn', () => {
            console.log(`🔄 تم إحياء البوت ${this.config.username}`);
            this.emit('respawn', {
                position: this.bot.entity.position
            });
        });

        // عند تغيير الصحة
        this.bot.on('health', () => {
            this.emit('health', {
                health: this.bot.health,
                food: this.bot.food
            });
        });

        // عند ركل البوت من السيرفر
        this.bot.on('kicked', (reason) => {
            console.log(`👢 تم ركل البوت: ${reason}`);
            this.emit('kicked', { reason });
        });
    }

    // تصنيف الأخطاء
    classifyError(error) {
        const message = error.message.toLowerCase();

        if (message.includes('timeout') || message.includes('timed out')) {
            return 'CONNECTION_TIMEOUT';
        }

        if (message.includes('econnrefused') || message.includes('connection refused')) {
            return 'CONNECTION_REFUSED';
        }

        if (message.includes('unsupported protocol version') ||
            message.includes('protocol version')) {
            return 'INVALID_VERSION';
        }

        if (message.includes('authentication') || message.includes('login') ||
            message.includes('invalid session')) {
            return 'AUTH_ERROR';
        }

        if (message.includes('kicked') || message.includes('disconnect')) {
            return 'KICKED';
        }

        return 'UNKNOWN';
    }

    handleReconnect() {
        if (!this.shouldReconnect) {
            console.log(`🛑 تم إيقاف إعادة الاتصال للبوت ${this.config.username}`);
            return;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log(`❌ السيرفر ${this.config.host}:${this.config.port} مطفي - فشل الاتصال بعد ${this.maxReconnectAttempts} محاولات (5 دقائق)`);
            this.emit('reconnectFailed');
            return;
        }

        this.reconnectAttempts++;

        // استخدام تأخير ثابت 20 ثانية بدلاً من التأخير المتزايد
        const delay = this.reconnectDelay; // 20 ثانية ثابتة

        console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts} خلال ${delay/1000} ثواني...`);

        this.reconnectTimeout = setTimeout(() => {
            if (this.shouldReconnect) {
                this.connect();
            }
        }, delay);
    }

    // إيقاف محاولات إعادة الاتصال
    stopReconnecting() {
        this.shouldReconnect = false;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        console.log(`🛑 تم إيقاف محاولات إعادة الاتصال للبوت ${this.config.username}`);
    }

    // قطع الاتصال نهائياً
    forceDisconnect() {
        this.stopReconnecting();
        if (this.bot) {
            try {
                this.bot.quit('إيقاف قسري');
            } catch (error) {
                console.log(`تجاهل خطأ قطع الاتصال القسري: ${error.message}`);
                // إذا فشل quit، نحاول end
                try {
                    if (this.bot._client) {
                        this.bot._client.end();
                    }
                } catch (endError) {
                    console.log(`تجاهل خطأ إنهاء الاتصال: ${endError.message}`);
                }
            }
            this.bot = null;
        }
        this.isConnected = false;
        this.resetConnection();
        console.log(`🔌 قطع الاتصال نهائياً مع البوت ${this.config.username}`);
    }

    // إرسال رسالة في الشات
    sendMessage(message) {
        if (this.isConnected && this.bot) {
            this.bot.chat(message);
            console.log(`📤 تم إرسال رسالة: ${message}`);
            return true;
        }
        return false;
    }

    // تنفيذ أمر
    executeCommand(command) {
        if (this.isConnected && this.bot) {
            this.bot.chat(`/${command}`);
            console.log(`⚡ تم تنفيذ الأمر: /${command}`);
            return true;
        }
        return false;
    }

    // إعادة تعيين حالة الاتصال
    resetConnection() {
        this.isConnected = false;
        this.connectionTime = null;
        this.reconnectAttempts = 0;
        this.connectionAttempts = 0;
        this.lastError = null;

        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }

        console.log(`🔄 تم إعادة تعيين حالة الاتصال للبوت ${this.config.username}`);
    }

    // الحصول على معلومات البوت
    getInfo() {
        if (!this.isConnected || !this.bot) {
            return {
                connected: false,
                username: this.config.username,
                server: `${this.config.host}:${this.config.port}`,
                lastError: this.lastError ? this.lastError.message : null,
                reconnectAttempts: this.reconnectAttempts,
                connectionAttempts: this.connectionAttempts
            };
        }

        return {
            connected: true,
            username: this.bot.username,
            server: `${this.config.host}:${this.config.port}`,
            version: this.bot.version || this.config.version,
            position: this.bot.entity ? this.bot.entity.position : null,
            health: this.bot.health,
            food: this.bot.food,
            experience: this.bot.experience,
            gameMode: this.bot.game ? this.bot.game.gameMode : null,
            dimension: this.bot.game ? this.bot.game.dimension : null,
            connectionTime: this.connectionTime,
            uptime: this.connectionTime ? Date.now() - this.connectionTime.getTime() : 0,
            ping: this.bot.player ? this.bot.player.ping : null,
            reconnectAttempts: this.reconnectAttempts,
            connectionAttempts: this.connectionAttempts
        };
    }

    // الحصول على قائمة اللاعبين المتصلين
    getPlayers() {
        if (!this.isConnected || !this.bot) {
            return [];
        }

        return Object.keys(this.bot.players).map(username => ({
            username,
            ping: this.bot.players[username].ping,
            gameMode: this.bot.players[username].gamemode
        }));
    }

    // قطع الاتصال
    disconnect() {
        console.log(`🔌 قطع الاتصال مع البوت ${this.config.username}`);

        // إيقاف إعادة الاتصال
        this.shouldReconnect = false;
        this.stopReconnecting();

        if (this.bot) {
            try {
                this.bot.quit('تم إيقاف البوت');
            } catch (error) {
                console.log(`تجاهل خطأ قطع الاتصال: ${error.message}`);
            }
            this.bot = null;
        }

        this.resetConnection();
        this.reconnectAttempts = this.maxReconnectAttempts; // منع إعادة الاتصال التلقائي
    }

    // تحديث إعدادات البوت
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    // فحص حالة الاتصال
    isAlive() {
        return this.isConnected && this.bot && this.bot.entity;
    }

    // الحصول على إحصائيات الأداء
    getPerformanceStats() {
        if (!this.isConnected || !this.bot) {
            return null;
        }

        return {
            ping: this.bot.player ? this.bot.player.ping : null,
            tps: this.bot.getTps ? this.bot.getTps() : null,
            memoryUsage: process.memoryUsage(),
            uptime: this.connectionTime ? Date.now() - this.connectionTime.getTime() : 0
        };
    }
}

module.exports = MinecraftJavaBot;
