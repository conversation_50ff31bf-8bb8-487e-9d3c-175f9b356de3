# إعدادات بوت التلغرام
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# معرفات الأدمن (مفصولة بفواصل)
ADMIN_IDS=123456789,987654321

# إعدادات قاعدة البيانات
# اختر نوع قاعدة البيانات المناسب لك

# SQLite (افتراضي - لا يحتاج إعداد إضافي)
DATABASE_URL=sqlite:./minecraft_bot.db

# PostgreSQL
# DATABASE_URL=********************************************/database_name
# POSTGRES_URL=********************************************/database_name

# MySQL
# DATABASE_URL=mysql://username:password@hostname:3306/database_name
# MYSQL_URL=mysql://username:password@hostname:3306/database_name

# MariaDB
# DATABASE_URL=mariadb://username:password@hostname:3306/database_name
# MARIADB_URL=mariadb://username:password@hostname:3306/database_name

# SQL Server
# DATABASE_URL=mssql://username:password@hostname:1433/database_name
# MSSQL_URL=mssql://username:password@hostname:1433/database_name

# Oracle Database
# DATABASE_URL=oracle://username:password@hostname:1521/database_name
# ORACLE_URL=oracle://username:password@hostname:1521/database_name

# MongoDB
# DATABASE_URL=********************************************************
# MONGODB_URL=********************************************************

# Redis
# DATABASE_URL=redis://username:password@hostname:6379
# REDIS_URL=redis://username:password@hostname:6379

# إعدادات البيئة
NODE_ENV=production
PORT=3000

# إعدادات الأمان
RATE_LIMIT_WINDOW=60000
MAX_REQUESTS_PER_WINDOW=30
MAX_BOTS_PER_USER=3

# إعدادات الشبكة
CONNECTION_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=30000
RESPONSE_TIMEOUT=10000

# إعدادات إعادة الاتصال
MAX_RECONNECT_ATTEMPTS=5
RECONNECT_DELAY=5000
BACKOFF_MULTIPLIER=1.5

# إعدادات المراقبة
MONITORING_ENABLED=true
MONITORING_INTERVAL=60000
PERFORMANCE_MONITORING=true

# إعدادات السجلات
LOG_LEVEL=info
LOG_TO_FILE=true
MAX_LOG_SIZE=10485760
MAX_LOG_FILES=5

# إعدادات الأداء
MAX_MEMORY_USAGE=536870912
GC_INTERVAL=300000
STATS_UPDATE_INTERVAL=60000

# إعدادات التنبيهات
ALERTS_ENABLED=true
MEMORY_THRESHOLD=80
ERROR_RATE_THRESHOLD=10
CONNECTION_FAILURE_THRESHOLD=5

# إعدادات البروكسي (اختياري)
PROXY_ENABLED=false
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# إعدادات DNS
DNS_TIMEOUT=5000
DNS_RETRIES=3
DNS_SERVERS=*******,*******

# إعدادات التطوير
DEBUG_MODE=false
HOT_RELOAD=false
TEST_MODE=false
SIMULATE_ERRORS=false

# إعدادات الإنتاج
ENABLE_GZIP=true
MINIFY_RESPONSES=true
CACHE_STATIC=true
CLUSTER_ENABLED=false

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
MAX_BACKUPS=7
BACKUP_PATH=./backups/

# إعدادات النسخ الاحتياطي التلقائي
AUTO_BACKUP_ENABLED=false
AUTO_BACKUP_INTERVAL=300000
AUTO_BACKUP_SEND_TO_ADMIN=true
AUTO_BACKUP_KEEP_IN_CHAT=true
MAX_AUTO_BACKUPS=20

# إعدادات ضغط وتشفير النسخ الاحتياطي
BACKUP_COMPRESSION=true
BACKUP_COMPRESSION_LEVEL=6
BACKUP_ENCRYPTION=false
BACKUP_PASSWORD=

# إعدادات التحسين
VACUUM_INTERVAL=604800000
ANALYZE_INTERVAL=86400000
CHECKPOINT_INTERVAL=3600000
