const { config } = require('./config');
const BotManager = require('./bot-manager');
const Database = require('./database');

/**
 * تحقق سريع من تحديث إصدارات Bedrock
 */
async function verifyBedrockUpdate() {
    console.log('🔍 التحقق من تحديث إصدارات Bedrock...\n');
    
    const expectedVersions = ['1.21.93', '1.21.92', '1.21.90', '1.21.80', '1.20.30'];
    let allGood = true;
    
    // 1. التحقق من config.js
    console.log('📋 فحص config.js:');
    const configVersions = config.supportedVersions.bedrock;
    const configMatch = JSON.stringify(expectedVersions) === JSON.stringify(configVersions);
    console.log(`   ${configMatch ? '✅' : '❌'} الإصدارات: ${configVersions.join(', ')}`);
    if (!configMatch) allGood = false;
    
    // 2. التحقق من bot-manager.js
    console.log('\n🤖 فحص bot-manager.js:');
    const botManager = new BotManager();
    const bmVersions = botManager.supportedVersions.bedrock;
    const bmMatch = JSON.stringify(expectedVersions) === JSON.stringify(bmVersions);
    console.log(`   ${bmMatch ? '✅' : '❌'} الإصدارات: ${bmVersions.join(', ')}`);
    if (!bmMatch) allGood = false;
    
    // 3. التحقق من قاعدة البيانات
    console.log('\n🗄️ فحص قاعدة البيانات:');
    try {
        const db = await new Database().init();
        const bots = await db.getAllBots();
        const bedrockBots = bots.filter(bot => bot.edition === 'bedrock');
        
        console.log(`   📊 إجمالي بوتات Bedrock: ${bedrockBots.length}`);
        
        let allBotsUpdated = true;
        for (const bot of bedrockBots) {
            const isSupported = expectedVersions.includes(bot.minecraft_version);
            console.log(`   ${isSupported ? '✅' : '❌'} ${bot.bot_name}: ${bot.minecraft_version}`);
            if (!isSupported) allBotsUpdated = false;
        }
        
        if (!allBotsUpdated) allGood = false;
        
    } catch (error) {
        console.log(`   ❌ خطأ في قاعدة البيانات: ${error.message}`);
        allGood = false;
    }
    
    // النتيجة النهائية
    console.log('\n' + '='.repeat(50));
    if (allGood) {
        console.log('🎉 تم التحديث بنجاح!');
        console.log('\n🎯 الإصدارات المدعومة الآن:');
        expectedVersions.forEach((version, index) => {
            const desc = index === 0 ? ' (أحدث إصدار)' : 
                        version === '1.20.30' ? ' (الإصدار القديم المطلوب)' : '';
            console.log(`   ${index + 1}. ${version}${desc}`);
        });
        console.log('\n✅ يمكنك تشغيل النظام الآن: npm start');
    } else {
        console.log('❌ هناك مشاكل في التحديث!');
        console.log('\n🔧 للإصلاح:');
        console.log('   1. تشغيل: node update-bedrock-versions-2025.js');
        console.log('   2. تشغيل: node test-bedrock-versions-2025.js');
        console.log('   3. إعادة المحاولة: node verify-bedrock-update.js');
    }
    console.log('='.repeat(50));
    
    return allGood;
}

// تشغيل التحقق
if (require.main === module) {
    verifyBedrockUpdate().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ خطأ في التحقق:', error);
        process.exit(1);
    });
}

module.exports = verifyBedrockUpdate;
