const Database = require('./database');
const BotManager = require('./bot-manager');
const { config } = require('./config');

/**
 * اختبار شامل للنظام المحسن
 * يختبر جميع الميزات الجديدة والمحسنة
 */
class EnhancedSystemTest {
    constructor() {
        this.db = null;
        this.botManager = null;
        this.testResults = [];
    }

    async init() {
        console.log('🔧 تهيئة اختبار النظام المحسن...');
        this.db = await new Database().init();
        this.botManager = await new BotManager().init();
        console.log('✅ تم تهيئة النظام للاختبار');
    }

    // اختبار الإصدارات المدعومة الجديدة
    async testSupportedVersions() {
        console.log('\n📋 اختبار الإصدارات المدعومة...');
        
        const expectedJavaVersions = ['1.21.4', '1.21.3', '1.21.1', '1.21.0', '1.20.6'];
        const expectedBedrockVersions = ['1.21.93', '1.21.92', '1.21.90', '1.21.80', '1.20.30'];
        
        const actualJavaVersions = this.botManager.supportedVersions.java;
        const actualBedrockVersions = this.botManager.supportedVersions.bedrock;
        
        // اختبار إصدارات Java
        const javaMatch = JSON.stringify(expectedJavaVersions) === JSON.stringify(actualJavaVersions);
        this.testResults.push({
            test: 'Java Versions',
            passed: javaMatch,
            expected: expectedJavaVersions,
            actual: actualJavaVersions
        });
        
        // اختبار إصدارات Bedrock
        const bedrockMatch = JSON.stringify(expectedBedrockVersions) === JSON.stringify(actualBedrockVersions);
        this.testResults.push({
            test: 'Bedrock Versions',
            passed: bedrockMatch,
            expected: expectedBedrockVersions,
            actual: actualBedrockVersions
        });
        
        console.log(`✅ Java Versions: ${javaMatch ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Bedrock Versions: ${bedrockMatch ? 'PASS' : 'FAIL'}`);
        
        if (javaMatch && bedrockMatch) {
            console.log('🎉 جميع الإصدارات محدثة بشكل صحيح!');
        }
    }

    // اختبار إعدادات إعادة الاتصال
    async testReconnectionSettings() {
        console.log('\n🔄 اختبار إعدادات إعادة الاتصال...');
        
        const expectedMaxAttempts = 15; // 15 محاولة (5 دقائق × 3 محاولات/دقيقة)
        const expectedDelay = 20000; // 20 ثانية
        
        const actualMaxAttempts = config.bots.reconnection.maxAttempts;
        const actualDelay = config.bots.reconnection.delay;
        
        const maxAttemptsMatch = expectedMaxAttempts === actualMaxAttempts;
        const delayMatch = expectedDelay === actualDelay;
        
        this.testResults.push({
            test: 'Max Reconnect Attempts',
            passed: maxAttemptsMatch,
            expected: expectedMaxAttempts,
            actual: actualMaxAttempts
        });
        
        this.testResults.push({
            test: 'Reconnect Delay',
            passed: delayMatch,
            expected: expectedDelay,
            actual: actualDelay
        });
        
        console.log(`✅ Max Attempts: ${maxAttemptsMatch ? 'PASS' : 'FAIL'} (${actualMaxAttempts})`);
        console.log(`✅ Delay: ${delayMatch ? 'PASS' : 'FAIL'} (${actualDelay}ms)`);
    }

    // اختبار نظام المراقبة
    async testMonitoringSystem() {
        console.log('\n🔍 اختبار نظام المراقبة...');
        
        // التحقق من أن المراقبة تعمل كل 20 ثانية
        const monitoringActive = this.botManager.monitoringInterval !== null;
        
        this.testResults.push({
            test: 'Monitoring System Active',
            passed: monitoringActive,
            expected: true,
            actual: monitoringActive
        });
        
        console.log(`✅ Monitoring Active: ${monitoringActive ? 'PASS' : 'FAIL'}`);
        
        if (monitoringActive) {
            console.log('🎯 نظام المراقبة نشط ويعمل كل 20 ثانية');
        }
    }

    // محاكاة اختبار نظام التحذيرات
    async simulateWarningSystem() {
        console.log('\n⚠️ محاكاة نظام التحذيرات...');
        
        // إنشاء بوت وهمي للاختبار
        const testBotData = {
            id: 999,
            bot_name: 'TestBot',
            server_host: 'test.server.com',
            server_port: 25565,
            user_id: 123456789
        };
        
        console.log('📤 محاكاة إرسال تحذير...');
        
        // محاكاة إرسال تحذير
        this.botManager.emit('serverDown', {
            botId: testBotData.id,
            botName: testBotData.bot_name,
            host: testBotData.server_host,
            port: testBotData.server_port,
            alertCount: 1,
            userId: testBotData.user_id,
            timeRemaining: Math.floor((15 - 1) * 20 / 60)
        });
        
        console.log('✅ تم إرسال تحذير تجريبي بنجاح');
        
        this.testResults.push({
            test: 'Warning System Simulation',
            passed: true,
            expected: 'Warning sent',
            actual: 'Warning sent successfully'
        });
    }

    // عرض نتائج الاختبار
    displayResults() {
        console.log('\n📊 نتائج الاختبار الشامل:');
        console.log('=' .repeat(50));
        
        let passedTests = 0;
        let totalTests = this.testResults.length;
        
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${index + 1}. ${result.test}: ${status}`);
            
            if (!result.passed) {
                console.log(`   Expected: ${JSON.stringify(result.expected)}`);
                console.log(`   Actual: ${JSON.stringify(result.actual)}`);
            }
            
            if (result.passed) passedTests++;
        });
        
        console.log('=' .repeat(50));
        console.log(`📈 النتيجة النهائية: ${passedTests}/${totalTests} اختبار نجح`);
        
        if (passedTests === totalTests) {
            console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
        }
        
        return passedTests === totalTests;
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        try {
            await this.init();
            
            console.log('🚀 بدء الاختبار الشامل للنظام المحسن...');
            console.log('🔧 الميزات المختبرة:');
            console.log('   • الإصدارات المدعومة الجديدة');
            console.log('   • نظام إعادة الاتصال المحسن (كل 20 ثانية)');
            console.log('   • نظام المراقبة والتحذيرات');
            console.log('   • نظام الإشعارات المطور');
            
            await this.testSupportedVersions();
            await this.testReconnectionSettings();
            await this.testMonitoringSystem();
            await this.simulateWarningSystem();
            
            const allPassed = this.displayResults();
            
            if (allPassed) {
                console.log('\n🎯 التوصيات:');
                console.log('✅ النظام محسن ومحدث بالكامل');
                console.log('✅ يدعم آخر 5 إصدارات Java وآخر 4 إصدارات Bedrock + 1.20.30');
                console.log('✅ يحاول إعادة الاتصال كل 20 ثانية لمدة 5 دقائق');
                console.log('✅ يرسل تحذيرات كل 20 ثانية (15 تحذير في 5 دقائق)');
                console.log('✅ يرسل إشعار فوري عند دخول البوت للعالم');
                console.log('✅ يرسل إشعار إيقاف نهائي بعد 5 دقائق من المحاولة');
            }
            
            return allPassed;
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل الاختبارات:', error);
            return false;
        }
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const test = new EnhancedSystemTest();
    test.runAllTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ فشل الاختبار:', error);
        process.exit(1);
    });
}

module.exports = EnhancedSystemTest;
