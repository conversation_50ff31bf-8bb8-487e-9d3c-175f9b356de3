const Database = require('./database');

/**
 * تحديث إصدارات Bedrock إلى الإصدارات الجديدة المطلوبة
 * الإصدارات الجديدة: 1.21.93, 1.21.92, 1.21.90, 1.21.80, 1.20.30
 */
async function updateBedrockVersions2025() {
    console.log('🔄 بدء تحديث إصدارات Bedrock إلى الإصدارات الجديدة...');
    console.log('📋 الإصدارات الجديدة المدعومة:');
    console.log('   • 1.21.93 (أحدث إصدار)');
    console.log('   • 1.21.92');
    console.log('   • 1.21.90');
    console.log('   • 1.21.80');
    console.log('   • 1.20.30 (الإصدار القديم المطلوب)');
    console.log('');
    
    const db = await new Database().init();
    
    // الحصول على جميع البوتات
    const bots = await db.getAllBots();
    
    // الإصدارات القديمة التي نريد تحديثها
    const oldVersions = [
        '1.21.50', '1.21.44', '1.21.40', '1.21.30',  // الإصدارات القديمة المدعومة سابقاً
        '1.21.84', '1.21.70', '1.21.60', '1.21.94'   // إصدارات أخرى قد تكون موجودة
    ];
    
    // خريطة تحديث الإصدارات
    const versionMapping = {
        '1.21.50': '1.21.93',  // أحدث إصدار قديم -> أحدث إصدار جديد
        '1.21.44': '1.21.92',
        '1.21.40': '1.21.90',
        '1.21.30': '1.21.80',
        '1.21.84': '1.21.93',  // إصدارات أخرى -> أحدث إصدار
        '1.21.70': '1.21.90',
        '1.21.60': '1.21.80',
        '1.21.94': '1.21.93'
    };
    
    let updatedCount = 0;
    let skippedCount = 0;
    const updateLog = [];
    
    console.log('🔍 فحص البوتات الموجودة...');
    
    for (const bot of bots) {
        if (bot.edition === 'bedrock') {
            const currentVersion = bot.minecraft_version;
            
            // إذا كان الإصدار الحالي مدعوم بالفعل، تخطيه
            if (['1.21.93', '1.21.92', '1.21.90', '1.21.80', '1.20.30'].includes(currentVersion)) {
                console.log(`✅ البوت ${bot.bot_name} يستخدم إصدار مدعوم بالفعل: ${currentVersion}`);
                skippedCount++;
                continue;
            }
            
            // تحديد الإصدار الجديد
            let newVersion = versionMapping[currentVersion];
            
            if (!newVersion) {
                // إذا لم يكن في الخريطة، استخدم أحدث إصدار
                newVersion = '1.21.93';
                console.log(`⚠️ البوت ${bot.bot_name} يستخدم إصدار غير معروف ${currentVersion}, سيتم تحديثه إلى ${newVersion}`);
            } else {
                console.log(`🔄 تحديث البوت ${bot.bot_name} من ${currentVersion} إلى ${newVersion}`);
            }
            
            // تحديث الإصدار في قاعدة البيانات
            const result = await db.updateBotVersion(bot.id, newVersion);
            if (result) {
                updatedCount++;
                updateLog.push({
                    botName: bot.bot_name,
                    oldVersion: currentVersion,
                    newVersion: newVersion,
                    status: 'نجح'
                });
                console.log(`   ✅ تم تحديث ${bot.bot_name} بنجاح`);
            } else {
                console.log(`   ❌ فشل في تحديث البوت ${bot.bot_name}`);
                updateLog.push({
                    botName: bot.bot_name,
                    oldVersion: currentVersion,
                    newVersion: newVersion,
                    status: 'فشل'
                });
            }
        }
    }
    
    // عرض ملخص التحديث
    console.log('\n' + '='.repeat(60));
    console.log('📊 ملخص تحديث إصدارات Bedrock:');
    console.log('='.repeat(60));
    console.log(`✅ تم تحديث: ${updatedCount} بوت`);
    console.log(`⏭️ تم تخطي: ${skippedCount} بوت (يستخدم إصدار مدعوم بالفعل)`);
    console.log(`📋 إجمالي بوتات Bedrock: ${updatedCount + skippedCount}`);
    
    if (updateLog.length > 0) {
        console.log('\n📝 تفاصيل التحديث:');
        updateLog.forEach((log, index) => {
            const status = log.status === 'نجح' ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${log.botName}: ${log.oldVersion} → ${log.newVersion}`);
        });
    }
    
    console.log('\n🎯 الإصدارات المدعومة الآن:');
    console.log('   • 1.21.93 (أحدث إصدار)');
    console.log('   • 1.21.92');
    console.log('   • 1.21.90');
    console.log('   • 1.21.80');
    console.log('   • 1.20.30 (الإصدار القديم المطلوب)');
    
    if (updatedCount > 0) {
        console.log('\n💡 نصائح:');
        console.log('   • أعد تشغيل البوتات المحدثة لتطبيق الإصدارات الجديدة');
        console.log('   • تأكد من أن السيرفرات تدعم الإصدارات الجديدة');
        console.log('   • يمكنك تشغيل النظام الآن: npm start');
    }
    
    console.log('\n🎉 تم إنجاز تحديث الإصدارات بنجاح!');
    
    process.exit(0);
}

// معالجة الأخطاء
updateBedrockVersions2025().catch(error => {
    console.error('❌ خطأ في تحديث إصدارات Bedrock:', error);
    console.error('\n🔧 للمساعدة:');
    console.error('   • تأكد من أن قاعدة البيانات متاحة');
    console.error('   • تحقق من صحة ملف config.js');
    console.error('   • جرب تشغيل: npm start');
    process.exit(1);
});
